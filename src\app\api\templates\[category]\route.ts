import { NextRequest, NextResponse } from 'next/server';
import { TemplateManager } from '@/lib/template-manager';

interface RouteParams {
  params: {
    category: string;
  };
}

/**
 * GET /api/templates/[category]
 * 获取指定分类的模板列表
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { category } = params;

    // 验证分类是否存在
    const categoryEnum = TemplateManager.getCategoryBySlug(category);
    if (!categoryEnum) {
      return NextResponse.json(
        { error: 'Category not found', category },
        { status: 404 }
      );
    }

    // 获取分类配置
    const categoryConfig = TemplateManager.getCategoryConfigBySlug(category);
    if (!categoryConfig) {
      return NextResponse.json(
        { error: 'Category configuration not found', category },
        { status: 404 }
      );
    }

    // 获取该分类的模板
    const templates = TemplateManager.getTemplatesByCategory(categoryEnum);

    // 解析查询参数
    const searchParams = request.nextUrl.searchParams;
    const orientation = searchParams.get('orientation') as 'portrait' | 'landscape' | null;
    const tags = searchParams.get('tags')?.split(',').filter(Boolean) || [];
    const limit = parseInt(searchParams.get('limit') || '0') || undefined;

    // 过滤模板
    let filteredTemplates = templates;

    // 按方向过滤
    if (orientation) {
      filteredTemplates = filteredTemplates.filter(template => 
        template.orientation === orientation
      );
    }

    // 按标签过滤
    if (tags.length > 0) {
      filteredTemplates = filteredTemplates.filter(template =>
        tags.some(tag => template.tags.includes(tag))
      );
    }

    // 限制数量
    if (limit) {
      filteredTemplates = filteredTemplates.slice(0, limit);
    }

    // 返回结果
    return NextResponse.json({
      category: {
        id: categoryConfig.id,
        name: categoryConfig.name,
        displayName: categoryConfig.displayName,
        description: categoryConfig.description,
        urlSlug: categoryConfig.urlSlug,
      },
      templates: filteredTemplates.map(template => ({
        id: template.id,
        name: template.name,
        displayName: template.displayName,
        description: template.description,
        preview: template.preview,
        backgroundImage: template.backgroundImage,
        orientation: template.orientation,
        aspectRatio: template.aspectRatio,
        category: template.category,
        tags: template.tags,
        seoTitle: template.seoTitle,
        seoDescription: template.seoDescription,
        seoKeywords: template.seoKeywords,
      })),
      total: templates.length,
      filtered: filteredTemplates.length,
      filters: {
        orientation,
        tags,
        limit,
      },
    });

  } catch (error) {
    console.error('Error fetching category templates:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/templates/[category]/stats
 * 获取分类统计信息
 */
export async function HEAD(request: NextRequest, { params }: RouteParams) {
  try {
    const { category } = params;

    // 验证分类是否存在
    const categoryEnum = TemplateManager.getCategoryBySlug(category);
    if (!categoryEnum) {
      return new NextResponse(null, { status: 404 });
    }

    // 获取模板统计
    const templates = TemplateManager.getTemplatesByCategory(categoryEnum);
    const portraitCount = templates.filter(t => t.orientation === 'portrait').length;
    const landscapeCount = templates.filter(t => t.orientation === 'landscape').length;

    // 设置响应头
    const headers = new Headers();
    headers.set('X-Total-Templates', templates.length.toString());
    headers.set('X-Portrait-Templates', portraitCount.toString());
    headers.set('X-Landscape-Templates', landscapeCount.toString());
    headers.set('X-Category', category);

    return new NextResponse(null, { 
      status: 200,
      headers 
    });

  } catch (error) {
    console.error('Error fetching category stats:', error);
    return new NextResponse(null, { status: 500 });
  }
}
