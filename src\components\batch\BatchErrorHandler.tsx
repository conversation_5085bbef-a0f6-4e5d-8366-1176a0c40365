'use client';

import React from 'react';
import { AlertTriangle, RefreshCw, Home, FileText, HelpCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface BatchErrorHandlerProps {
  error: {
    type: 'upload' | 'generation' | 'download' | 'network' | 'validation';
    message: string;
    details?: string;
    code?: string;
  };
  onRetry?: () => void;
  onGoHome?: () => void;
  onContactSupport?: () => void;
}

export default function BatchErrorHandler({
  error,
  onRetry,
  onGoHome,
  onContactSupport
}: BatchErrorHandlerProps) {
  
  // Get error type display information
  const getErrorTypeInfo = (type: string) => {
    switch (type) {
      case 'upload':
        return {
          title: 'File Upload Failed',
          icon: <FileText className="h-8 w-8 text-red-500" />,
          color: 'bg-red-50 border-red-200',
          suggestions: [
            'Check if file format is correct (supports .xlsx, .xls, .csv)',
            'Ensure file size does not exceed 10MB',
            'Verify file content format meets requirements',
            'Try re-saving the file and uploading again'
          ]
        };
      case 'generation':
        return {
          title: 'Certificate Generation Failed',
          icon: <AlertTriangle className="h-8 w-8 text-orange-500" />,
          color: 'bg-orange-50 border-orange-200',
          suggestions: [
            'Check if data format is correct',
            'Ensure all required fields are filled',
            'Try reducing the batch generation quantity',
            'Check if network connection is stable'
          ]
        };
      case 'download':
        return {
          title: 'Download Failed',
          icon: <AlertTriangle className="h-8 w-8 text-blue-500" />,
          color: 'bg-blue-50 border-blue-200',
          suggestions: [
            'Check if network connection is stable',
            'Ensure browser allows file downloads',
            'Try refreshing the page and downloading again',
            'Check if device has sufficient storage space'
          ]
        };
      case 'network':
        return {
          title: 'Network Connection Error',
          icon: <RefreshCw className="h-8 w-8 text-purple-500" />,
          color: 'bg-purple-50 border-purple-200',
          suggestions: [
            'Check if network connection is working',
            'Try refreshing the page',
            'Check firewall or proxy settings',
            'Try again later'
          ]
        };
      case 'validation':
        return {
          title: 'Data Validation Failed',
          icon: <HelpCircle className="h-8 w-8 text-yellow-500" />,
          color: 'bg-yellow-50 border-yellow-200',
          suggestions: [
            'Check if data format is correct',
            'Ensure all required fields are filled',
            'Verify character length meets requirements',
            'Check if special characters are handled correctly'
          ]
        };
      default:
        return {
          title: 'Unknown Error',
          icon: <AlertTriangle className="h-8 w-8 text-gray-500" />,
          color: 'bg-gray-50 border-gray-200',
          suggestions: [
            'Try refreshing the page',
            'Check network connection',
            'Try again later',
            'Contact technical support'
          ]
        };
    }
  };

  const errorInfo = getErrorTypeInfo(error.type);

  return (
    <Card className="p-6 max-w-2xl mx-auto">
      <div className="space-y-6">
        {/* 错误头部 */}
        <div className="text-center">
          <div className="flex justify-center mb-4">
            {errorInfo.icon}
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            {errorInfo.title}
          </h2>
          <Badge variant="destructive" className="mb-4">
            Error Code: {error.code || 'UNKNOWN'}
          </Badge>
        </div>

        {/* Error Information */}
        <div className={`p-4 rounded-lg border ${errorInfo.color}`}>
          <h3 className="font-medium text-gray-900 mb-2">Error Details</h3>
          <p className="text-gray-700 mb-2">{error.message}</p>
          {error.details && (
            <p className="text-sm text-gray-600 font-mono bg-white p-2 rounded border">
              {error.details}
            </p>
          )}
        </div>

        {/* Solution Suggestions */}
        <div className="space-y-3">
          <h3 className="font-medium text-gray-900">Suggested Solutions</h3>
          <ul className="space-y-2">
            {errorInfo.suggestions.map((suggestion, index) => (
              <li key={index} className="flex items-start space-x-2">
                <span className="flex-shrink-0 w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mt-0.5">
                  {index + 1}
                </span>
                <span className="text-sm text-gray-700">{suggestion}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-200">
          {onRetry && (
            <Button
              onClick={onRetry}
              className="flex-1 bg-blue-600 hover:bg-blue-700"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          )}

          {onGoHome && (
            <Button
              variant="outline"
              onClick={onGoHome}
              className="flex-1"
            >
              <Home className="h-4 w-4 mr-2" />
              Go Home
            </Button>
          )}

          {onContactSupport && (
            <Button
              variant="outline"
              onClick={onContactSupport}
              className="flex-1"
            >
              <HelpCircle className="h-4 w-4 mr-2" />
              Contact Support
            </Button>
          )}
        </div>

        {/* FAQ */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2">Frequently Asked Questions</h4>
          <div className="space-y-2 text-sm text-gray-600">
            <details className="cursor-pointer">
              <summary className="font-medium hover:text-gray-900">
                What file formats are supported?
              </summary>
              <p className="mt-1 pl-4">
                Supports Excel files (.xlsx, .xls) and CSV files. File size cannot exceed 10MB.
              </p>
            </details>

            <details className="cursor-pointer">
              <summary className="font-medium hover:text-gray-900">
                What are the data format requirements?
              </summary>
              <p className="mt-1 pl-4">
                Column A: Recipient Name, Column B: Date, Column C: Signature, Column D: Certificate Details. Header row recommended.
              </p>
            </details>

            <details className="cursor-pointer">
              <summary className="font-medium hover:text-gray-900">
                How many certificates can be generated at once?
              </summary>
              <p className="mt-1 pl-4">
                Maximum 1000 certificates per batch. For more, please process in multiple batches.
              </p>
            </details>
          </div>
        </div>
      </div>
    </Card>
  );
}
