import { MetadataRoute } from 'next'
import { TemplateManager } from '@/lib/template-manager'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://certificatemaker.com'
  const currentDate = new Date()

  // 基础页面
  const routes: MetadataRoute.Sitemap = [
    {
      url: baseUrl,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 1.0,
    },
    {
      url: `${baseUrl}/certificate-templates`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/privacy`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.4,
    },
    {
      url: `${baseUrl}/terms`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.4,
    },
  ]

  try {
    // 获取启用的分类并添加到sitemap
    const categories = TemplateManager.getEnabledCategories()
    
    categories.forEach(category => {
      routes.push({
        url: `${baseUrl}/certificate-templates/${category.urlSlug}`,
        lastModified: currentDate,
        changeFrequency: 'weekly',
        priority: 0.8,
      })
    })
  } catch (error) {
    console.error('Error loading categories for sitemap:', error)
    // 如果获取分类失败，至少包含基础页面
  }

  return routes
}
