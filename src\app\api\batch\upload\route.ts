import { NextRequest, NextResponse } from 'next/server';
import { ServerFileParser } from '@/lib/server-file-parser';
import { FileUploadResult } from '@/types/certificate';

/**
 * 批量文件上传和解析API
 * POST /api/batch/upload
 */
export async function POST(request: NextRequest) {
  try {
    console.log('📤 Batch file upload request received');

    // 获取表单数据
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json(
        { error: '没有上传文件' },
        { status: 400 }
      );
    }

    console.log(`📁 Processing file: ${file.name} (${file.size} bytes)`);

    // 解析文件选项
    const hasHeader = formData.get('hasHeader') === 'true';
    const skipEmptyRows = formData.get('skipEmptyRows') !== 'false';
    const maxRows = parseInt(formData.get('maxRows') as string) || 1000;

    // 列映射配置
    const columnMapping = {
      recipientName: formData.get('recipientNameColumn') as string || 'A',
      date: formData.get('dateColumn') as string || 'B',
      signature: formData.get('signatureColumn') as string || 'C',
      details: formData.get('detailsColumn') as string || 'D'
    };

    // 解析文件
    const result: FileUploadResult = await ServerFileParser.parseFile(file, {
      hasHeader,
      skipEmptyRows,
      maxRows,
      columnMapping
    });

    if (!result.success) {
      console.error('❌ File parsing failed:', result.errors);
      return NextResponse.json(
        { 
          error: '文件解析失败',
          details: result.errors 
        },
        { status: 400 }
      );
    }

    console.log(`✅ File parsed successfully: ${result.validRows}/${result.totalRows} valid rows`);

    // 返回解析结果
    return NextResponse.json({
      success: true,
      data: result.data,
      totalRows: result.totalRows,
      validRows: result.validRows,
      errors: result.errors || [],
      summary: {
        fileName: file.name,
        fileSize: file.size,
        totalRows: result.totalRows,
        validRows: result.validRows,
        errorCount: result.errors?.length || 0
      }
    });

  } catch (error) {
    console.error('❌ Batch upload API error:', error);
    
    return NextResponse.json(
      { 
        error: '服务器内部错误',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

/**
 * 获取上传配置信息
 * GET /api/batch/upload
 */
export async function GET() {
  try {
    return NextResponse.json({
      config: {
        maxFileSize: 10 * 1024 * 1024, // 10MB
        maxRows: 1000,
        supportedFormats: ['.xlsx', '.xls', '.csv'],
        defaultColumnMapping: {
          recipientName: 'A',
          date: 'B', 
          signature: 'C',
          details: 'D'
        },
        sampleData: [
          {
            recipientName: '张三',
            date: '2024-01-15',
            signature: '李主任',
            details: '完成了高级项目管理课程'
          },
          {
            recipientName: '李四',
            date: '2024-01-16', 
            signature: '王经理',
            details: '在团队协作中表现优秀'
          }
        ]
      }
    });

  } catch (error) {
    console.error('❌ Get upload config error:', error);
    
    return NextResponse.json(
      { error: '获取配置失败' },
      { status: 500 }
    );
  }
}
