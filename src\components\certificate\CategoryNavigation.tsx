'use client';

import Link from 'next/link';
import { CertificateCategory } from '@/types/certificate';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Award, BookOpen, Users, Star, Settings } from 'lucide-react';

interface CategoryNavigationProps {
  currentCategory?: CertificateCategory;
  showTitle?: boolean;
  maxCategories?: number;
}

export default function CategoryNavigation({
  currentCategory,
  showTitle = true,
  maxCategories
}: CategoryNavigationProps) {
  // 直接定义分类数据，避免循环依赖
  const allCategories = [
    {
      id: CertificateCategory.ACHIEVEMENT,
      name: 'achievement',
      displayName: 'Achievement Certificates',
      description: 'Professional certificates for awards and recognition',
      urlSlug: 'achievement',
      templateCount: 2
    },
    {
      id: CertificateCategory.COMPLETION,
      name: 'completion',
      displayName: 'Completion Certificates',
      description: 'Certificates for course and training completion',
      urlSlug: 'completion',
      templateCount: 2
    },
    {
      id: CertificateCategory.PARTICIPATION,
      name: 'participation',
      displayName: 'Participation Certificates',
      description: 'Certificates for event and workshop participation',
      urlSlug: 'participation',
      templateCount: 2
    },
    {
      id: CertificateCategory.EXCELLENCE,
      name: 'excellence',
      displayName: 'Excellence Certificates',
      description: 'Certificates for outstanding performance and excellence',
      urlSlug: 'excellence',
      templateCount: 2
    },
    {
      id: CertificateCategory.CUSTOM,
      name: 'custom',
      displayName: 'Custom Certificates',
      description: 'Customizable certificates for any purpose',
      urlSlug: 'custom',
      templateCount: 0
    }
  ];

  const displayCategories = maxCategories
    ? allCategories.slice(0, maxCategories)
    : allCategories;

  // 获取分类图标
  const getCategoryIcon = (categoryId: string) => {
    switch (categoryId) {
      case 'achievement': return <Award className="w-5 h-5" />;
      case 'completion': return <BookOpen className="w-5 h-5" />;
      case 'participation': return <Users className="w-5 h-5" />;
      case 'excellence': return <Star className="w-5 h-5" />;
      case 'custom': return <Settings className="w-5 h-5" />;
      default: return <Award className="w-5 h-5" />;
    }
  };

  // 获取分类颜色
  const getCategoryColor = (categoryId: string) => {
    switch (categoryId) {
      case 'achievement': return 'text-blue-600 bg-blue-100';
      case 'completion': return 'text-green-600 bg-green-100';
      case 'participation': return 'text-purple-600 bg-purple-100';
      case 'excellence': return 'text-yellow-600 bg-yellow-100';
      case 'custom': return 'text-gray-600 bg-gray-100';
      default: return 'text-blue-600 bg-blue-100';
    }
  };

  return (
    <div className="w-full">
      {showTitle && (
        <div className="text-center mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">
            Browse All Certificate Categories
          </h2>
          <p className="text-gray-600">
            Find the perfect template for your needs
          </p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
        {displayCategories.map((category) => {
          const isActive = currentCategory === category.id;
          const colorClasses = getCategoryColor(category.id);
          
          return (
            <Link
              key={category.id}
              href={`/${category.urlSlug}/`}
              className={`group block ${isActive ? 'pointer-events-none' : ''}`}
            >
              <Card className={`h-full transition-all duration-200 ${
                isActive 
                  ? 'ring-2 ring-blue-500 bg-blue-50' 
                  : 'hover:shadow-lg hover:scale-105'
              }`}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className={`p-2 rounded-lg ${colorClasses} ${
                      isActive ? 'bg-blue-200' : ''
                    }`}>
                      {getCategoryIcon(category.id)}
                    </div>
                    {isActive && (
                      <Badge variant="default" className="text-xs">
                        Current
                      </Badge>
                    )}
                  </div>
                </CardHeader>
                
                <CardContent className="pt-0">
                  <CardTitle className={`text-lg mb-2 ${
                    isActive 
                      ? 'text-blue-900' 
                      : 'group-hover:text-blue-600 transition-colors'
                  }`}>
                    {category.displayName}
                  </CardTitle>
                  
                  <CardDescription className="text-sm mb-3 line-clamp-2">
                    {category.description}
                  </CardDescription>
                  
                  <div className="flex items-center justify-between">
                    <Badge
                      variant={isActive ? "default" : "secondary"}
                      className="text-xs"
                    >
                      {category.templateCount} template{category.templateCount !== 1 ? 's' : ''}
                    </Badge>

                    {category.templateCount > 0 && (
                      <div className="flex space-x-1">
                        {/* 简化的模板指示器 */}
                        {Array.from({ length: Math.min(category.templateCount, 3) }).map((_, index) => (
                          <div
                            key={index}
                            className="w-2 h-2 rounded-full bg-blue-400"
                          />
                        ))}
                        {category.templateCount > 3 && (
                          <div className="text-xs text-gray-400">
                            +{category.templateCount - 3}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </Link>
          );
        })}
      </div>

      {/* 查看所有分类链接 */}
      {maxCategories && allCategories.length > maxCategories && (
        <div className="text-center mt-6">
          <Link
            href="/"
            className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
          >
            View All Categories
            <Award className="w-4 h-4 ml-2" />
          </Link>
        </div>
      )}
    </div>
  );
}
