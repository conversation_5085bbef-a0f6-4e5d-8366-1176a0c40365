import archiver from 'archiver';
import fs from 'fs';
import path from 'path';

/**
 * ZIP打包工具
 * 负责将批量生成的PDF文件打包为ZIP文件
 */
export class ZipPackager {
  /**
   * 将目录中的所有PDF文件打包为ZIP
   */
  static async packDirectory(
    sourceDir: string,
    outputPath: string,
    options: {
      compressionLevel?: number;
      includeSubdirectories?: boolean;
      fileFilter?: (filename: string) => boolean;
    } = {}
  ): Promise<{
    success: boolean;
    zipPath?: string;
    fileCount?: number;
    totalSize?: number;
    error?: string;
  }> {
    return new Promise((resolve) => {
      try {
        // 检查源目录是否存在
        if (!fs.existsSync(sourceDir)) {
          resolve({
            success: false,
            error: '源目录不存在'
          });
          return;
        }

        // 确保输出目录存在
        const outputDir = path.dirname(outputPath);
        if (!fs.existsSync(outputDir)) {
          fs.mkdirSync(outputDir, { recursive: true });
        }

        // 创建输出流
        const output = fs.createWriteStream(outputPath);
        const archive = archiver('zip', {
          zlib: { level: options.compressionLevel || 6 } // 压缩级别
        });

        let fileCount = 0;
        let totalSize = 0;

        // 监听事件
        output.on('close', () => {
          console.log(`📦 ZIP package created: ${outputPath}`);
          console.log(`📊 Total files: ${fileCount}, Size: ${archive.pointer()} bytes`);
          
          resolve({
            success: true,
            zipPath: outputPath,
            fileCount,
            totalSize: archive.pointer()
          });
        });

        archive.on('error', (err) => {
          console.error('❌ ZIP packaging error:', err);
          resolve({
            success: false,
            error: err.message
          });
        });

        archive.on('entry', (entry) => {
          if (entry.type === 'file') {
            fileCount++;
            totalSize += entry.stats?.size || 0;
          }
        });

        // 连接输出流
        archive.pipe(output);

        // 添加文件到压缩包
        const fileFilter = options.fileFilter || ((filename: string) => filename.endsWith('.pdf'));
        
        if (options.includeSubdirectories !== false) {
          // 递归添加目录
          archive.directory(sourceDir, false, (entry) => {
            return fileFilter(entry.name) ? entry : false;
          });
        } else {
          // 只添加当前目录的文件
          const files = fs.readdirSync(sourceDir);
          for (const file of files) {
            const filePath = path.join(sourceDir, file);
            const stat = fs.statSync(filePath);
            
            if (stat.isFile() && fileFilter(file)) {
              archive.file(filePath, { name: file });
            }
          }
        }

        // 完成打包
        archive.finalize();

      } catch (error) {
        console.error('❌ ZIP packaging failed:', error);
        resolve({
          success: false,
          error: error instanceof Error ? error.message : '打包失败'
        });
      }
    });
  }

  /**
   * 打包指定的文件列表
   */
  static async packFiles(
    files: { path: string; name?: string }[],
    outputPath: string,
    options: {
      compressionLevel?: number;
    } = {}
  ): Promise<{
    success: boolean;
    zipPath?: string;
    fileCount?: number;
    totalSize?: number;
    error?: string;
  }> {
    return new Promise((resolve) => {
      try {
        // 确保输出目录存在
        const outputDir = path.dirname(outputPath);
        if (!fs.existsSync(outputDir)) {
          fs.mkdirSync(outputDir, { recursive: true });
        }

        // 创建输出流
        const output = fs.createWriteStream(outputPath);
        const archive = archiver('zip', {
          zlib: { level: options.compressionLevel || 6 }
        });

        let fileCount = 0;

        // 监听事件
        output.on('close', () => {
          console.log(`📦 ZIP package created: ${outputPath}`);
          console.log(`📊 Total files: ${fileCount}, Size: ${archive.pointer()} bytes`);
          
          resolve({
            success: true,
            zipPath: outputPath,
            fileCount,
            totalSize: archive.pointer()
          });
        });

        archive.on('error', (err) => {
          console.error('❌ ZIP packaging error:', err);
          resolve({
            success: false,
            error: err.message
          });
        });

        // 连接输出流
        archive.pipe(output);

        // 添加文件
        for (const file of files) {
          if (fs.existsSync(file.path)) {
            const fileName = file.name || path.basename(file.path);
            archive.file(file.path, { name: fileName });
            fileCount++;
          } else {
            console.warn(`⚠️ File not found: ${file.path}`);
          }
        }

        // 完成打包
        archive.finalize();

      } catch (error) {
        console.error('❌ ZIP packaging failed:', error);
        resolve({
          success: false,
          error: error instanceof Error ? error.message : '打包失败'
        });
      }
    });
  }

  /**
   * 生成ZIP文件名
   */
  static generateZipFilename(prefix: string = 'certificates'): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
    const randomStr = Math.random().toString(36).substring(2, 8);
    return `${prefix}_${timestamp}_${randomStr}.zip`;
  }

  /**
   * 获取文件大小（字节）
   */
  static getFileSize(filePath: string): number {
    try {
      const stats = fs.statSync(filePath);
      return stats.size;
    } catch {
      return 0;
    }
  }

  /**
   * 格式化文件大小
   */
  static formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  /**
   * 清理临时ZIP文件
   */
  static async cleanupZipFile(zipPath: string): Promise<void> {
    try {
      if (fs.existsSync(zipPath)) {
        fs.unlinkSync(zipPath);
        console.log(`🧹 Cleaned up ZIP file: ${zipPath}`);
      }
    } catch (error) {
      console.error(`Failed to cleanup ZIP file ${zipPath}:`, error);
    }
  }

  /**
   * 验证ZIP文件
   */
  static validateZipFile(zipPath: string): {
    valid: boolean;
    size?: number;
    error?: string;
  } {
    try {
      if (!fs.existsSync(zipPath)) {
        return {
          valid: false,
          error: 'ZIP文件不存在'
        };
      }

      const stats = fs.statSync(zipPath);
      
      if (stats.size === 0) {
        return {
          valid: false,
          error: 'ZIP文件为空'
        };
      }

      return {
        valid: true,
        size: stats.size
      };

    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : '验证失败'
      };
    }
  }
}
