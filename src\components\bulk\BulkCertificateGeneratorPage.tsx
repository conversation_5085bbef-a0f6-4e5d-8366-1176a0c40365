'use client';

import React, { useState, useCallback } from 'react';
import { 
  Users, 
  FileText, 
  Upload, 
  Download, 
  ArrowRight, 
  Clock, 
  CheckCircle, 
  Star,
  Zap,
  Shield,
  Globe,
  Award,
  FileSpreadsheet,
  Play
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import BatchCertificateMaker from '@/components/batch/BatchCertificateMaker';


export default function BulkCertificateGeneratorPage() {
  // Handle scroll to generator
  const scrollToGenerator = useCallback(() => {
    const generatorElement = document.getElementById('bulk-generator');
    if (generatorElement) {
      generatorElement.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);

  const features = [
    {
      icon: <Zap className="h-8 w-8 text-blue-600" />,
      title: 'Lightning Fast Generation',
      description: 'Generate 100+ certificates in under 5 minutes with our optimized bulk processing engine.'
    },
    {
      icon: <FileSpreadsheet className="h-8 w-8 text-green-600" />,
      title: 'Excel & CSV Support',
      description: 'Upload your data in Excel (.xlsx, .xls) or CSV format. Download our sample templates to get started.'
    },
    {
      icon: <Award className="h-8 w-8 text-purple-600" />,
      title: 'Professional Templates',
      description: 'Choose from dozens of professionally designed certificate templates for any occasion.'
    },
    {
      icon: <Download className="h-8 w-8 text-orange-600" />,
      title: 'ZIP Download',
      description: 'Get all your certificates in a single ZIP file, perfectly organized and ready to distribute.'
    },
    {
      icon: <Shield className="h-8 w-8 text-red-600" />,
      title: 'Secure & Private',
      description: 'Your data is processed securely and never stored on our servers. Complete privacy guaranteed.'
    },
    {
      icon: <Globe className="h-8 w-8 text-indigo-600" />,
      title: 'No Registration Required',
      description: 'Start generating certificates immediately. No account creation or subscription needed.'
    }
  ];

  const steps = [
    {
      number: '01',
      title: 'Choose Your Template',
      description: 'Select from our collection of professional certificate templates designed for various purposes.',
      icon: <FileText className="h-6 w-6" />
    },
    {
      number: '02',
      title: 'Upload Your Data',
      description: 'Upload an Excel or CSV file with recipient information. Use our sample template for proper formatting.',
      icon: <Upload className="h-6 w-6" />
    },
    {
      number: '03',
      title: 'Generate in Bulk',
      description: 'Our system automatically processes all certificates with real-time progress tracking.',
      icon: <Users className="h-6 w-6" />
    },
    {
      number: '04',
      title: 'Download ZIP File',
      description: 'Download all certificates in a single ZIP file, perfectly organized and ready to distribute.',
      icon: <Download className="h-6 w-6" />
    }
  ];

  const benefits = [
    'Generate up to 1000 certificates per batch',
    'Professional PDF quality output',
    'Real-time progress tracking',
    'Automatic error detection and validation',
    'Mobile-friendly interface',
    'Completely free to use'
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section */}
      <section className="relative py-20 px-4 text-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 to-purple-600/5"></div>
        <div className="relative max-w-6xl mx-auto">


          <div className="space-y-6">
            <Badge variant="secondary" className="bg-blue-100 text-blue-800 px-4 py-2">
              <Star className="w-4 h-4 mr-2" />
              #1 Free Bulk Certificate Generator
            </Badge>
            
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 leading-tight">
              Bulk Certificate Generator
              <span className="block text-blue-600 mt-2">Create Multiple Certificates Instantly</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              Generate hundreds of professional certificates in minutes using Excel or CSV files. 
              Perfect for schools, organizations, training programs, and events. 
              <strong className="text-gray-900"> Completely free</strong> and no registration required.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mt-12">
            <Button 
              size="lg" 
              onClick={scrollToGenerator}
              className="text-lg px-8 py-6 h-auto min-h-[64px] bg-blue-600 hover:bg-blue-700 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center"
            >
              <Play className="w-6 h-6 mr-3" />
              <span className="text-center">Generate Multiple Certificates</span>
              <ArrowRight className="w-6 h-6 ml-3" />
            </Button>
            <div className="text-sm text-gray-500 flex items-center">
              <Clock className="h-5 w-5 mr-2" />
              Generate 100+ certificates in under 5 minutes
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-16 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">1000+</div>
              <div className="text-sm text-gray-600">Certificates per batch</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">5 min</div>
              <div className="text-sm text-gray-600">Average processing time</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">50+</div>
              <div className="text-sm text-gray-600">Professional templates</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600">100%</div>
              <div className="text-sm text-gray-600">Free to use</div>
            </div>
          </div>
        </div>
      </section>

      {/* Certificate Generator */}
      <section id="bulk-generator" className="py-16 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <BatchCertificateMaker />
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose Our Bulk Certificate Generator?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Powerful features designed to make bulk certificate generation fast, easy, and professional.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                <CardContent className="p-8 text-center">
                  <div className="mb-4 flex justify-center">
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              How to Use the Bulk Certificate Generator
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Follow these simple steps to generate hundreds of professional certificates in minutes.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {steps.map((step, index) => (
              <div key={index} className="text-center">
                <div className="relative mb-6">
                  <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                    {step.number}
                  </div>
                  <div className="absolute top-8 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    {step.icon}
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {step.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {step.description}
                </p>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Button
              size="lg"
              onClick={scrollToGenerator}
              className="text-lg px-8 py-4 h-auto min-h-[56px] bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Users className="w-5 h-5 mr-2" />
              Start Bulk Certificate Generation
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Everything You Need for Bulk Certificate Generation
              </h2>
              <p className="text-xl text-gray-600 mb-8">
                Our bulk certificate generator includes all the features you need to create professional certificates efficiently and effectively.
              </p>

              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="h-6 w-6 text-green-600 flex-shrink-0" />
                    <span className="text-gray-700 text-lg">{benefit}</span>
                  </div>
                ))}
              </div>

              <div className="mt-8">
                <Button
                  size="lg"
                  onClick={scrollToGenerator}
                  className="text-lg px-8 py-4 h-auto min-h-[56px] bg-green-600 hover:bg-green-700 shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  <Award className="w-5 h-5 mr-2" />
                  Generate Professional Certificates
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </div>
            </div>

            <div className="lg:pl-8">
              <Card className="border-0 shadow-2xl">
                <CardContent className="p-8">
                  <div className="text-center mb-6">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <FileSpreadsheet className="h-8 w-8 text-blue-600" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      Sample Data Format
                    </h3>
                    <p className="text-gray-600">
                      Use this format for your Excel or CSV file:
                    </p>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4 font-mono text-sm">
                    <div className="grid grid-cols-4 gap-2 mb-2 font-bold text-gray-700">
                      <div>Name</div>
                      <div>Date</div>
                      <div>Signature</div>
                      <div>Details</div>
                    </div>
                    <div className="grid grid-cols-4 gap-2 text-gray-600">
                      <div>John Doe</div>
                      <div>2024-01-15</div>
                      <div>Director</div>
                      <div>Course Completion</div>
                    </div>
                    <div className="grid grid-cols-4 gap-2 text-gray-600">
                      <div>Jane Smith</div>
                      <div>2024-01-16</div>
                      <div>Manager</div>
                      <div>Training Program</div>
                    </div>
                  </div>

                  <div className="mt-6 text-center">
                    <p className="text-sm text-gray-500 mb-4">
                      Download our sample template to get started quickly
                    </p>
                    <div className="flex gap-2 justify-center">
                      <Button variant="outline" size="sm">
                        <Download className="w-4 h-4 mr-2" />
                        Excel Template
                      </Button>
                      <Button variant="outline" size="sm">
                        <Download className="w-4 h-4 mr-2" />
                        CSV Template
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Generate Bulk Certificates?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Join thousands of organizations who trust our bulk certificate generator for their important events and programs.
            Start creating professional certificates in minutes - completely free!
          </p>
          <Button
            size="lg"
            onClick={scrollToGenerator}
            className="text-lg px-8 py-6 h-auto min-h-[64px] bg-white text-blue-600 hover:bg-gray-100 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center"
          >
            <Users className="w-6 h-6 mr-3" />
            <span className="text-center">Generate Multiple Certificates</span>
            <ArrowRight className="w-6 h-6 ml-3" />
          </Button>
          <div className="mt-6 text-sm opacity-75">
            No registration required • Completely free • Generate up to 1000 certificates per batch
          </div>
        </div>
      </section>


    </div>
  );
}
