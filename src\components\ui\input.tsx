import * as React from "react"

import { cn } from "@/lib/utils"

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean
  characterCount?: {
    current: number
    max: number
  }
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, error, characterCount, ...props }, ref) => {
    return (
      <div className="relative">
        <input
          type={type}
          className={cn(
            "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
            // 移动端优化
            "md:text-sm text-base", // 防止iOS缩放
            "min-h-[44px] md:min-h-[40px]", // 触摸友好
            error && "border-red-500 focus-visible:ring-red-500",
            className
          )}
          ref={ref}
          {...props}
        />
        {characterCount && (
          <div className={cn(
            "absolute right-2 top-1/2 -translate-y-1/2 text-xs",
            characterCount.current > characterCount.max * 0.9 && "text-yellow-600",
            characterCount.current >= characterCount.max && "text-red-600",
            characterCount.current <= characterCount.max * 0.8 && "text-muted-foreground"
          )}>
            {characterCount.current}/{characterCount.max}
          </div>
        )}
      </div>
    )
  }
)
Input.displayName = "Input"

export { Input }
