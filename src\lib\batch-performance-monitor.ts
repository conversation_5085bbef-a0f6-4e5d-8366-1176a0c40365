/**
 * 批量生成性能监控工具
 * 用于监控和优化批量PDF生成的性能
 */
export class BatchPerformanceMonitor {
  private static metrics = new Map<string, any>();
  private static readonly MAX_METRICS_HISTORY = 100;

  /**
   * 开始监控任务
   */
  static startTask(taskId: string, totalItems: number): void {
    const startTime = Date.now();
    
    this.metrics.set(taskId, {
      taskId,
      totalItems,
      startTime,
      endTime: null,
      completedItems: 0,
      failedItems: 0,
      memoryUsage: this.getMemoryUsage(),
      processingTimes: [],
      errors: [],
      status: 'running'
    });

    console.log(`📊 Performance monitoring started for task: ${taskId}`);
  }

  /**
   * 记录单个项目处理完成
   */
  static recordItemCompleted(taskId: string, processingTime: number): void {
    const metrics = this.metrics.get(taskId);
    if (!metrics) return;

    metrics.completedItems++;
    metrics.processingTimes.push(processingTime);
    
    // 只保留最近的100个处理时间记录
    if (metrics.processingTimes.length > this.MAX_METRICS_HISTORY) {
      metrics.processingTimes = metrics.processingTimes.slice(-this.MAX_METRICS_HISTORY);
    }

    // 更新内存使用情况
    metrics.memoryUsage = this.getMemoryUsage();

    this.metrics.set(taskId, metrics);
  }

  /**
   * 记录项目处理失败
   */
  static recordItemFailed(taskId: string, error: string): void {
    const metrics = this.metrics.get(taskId);
    if (!metrics) return;

    metrics.failedItems++;
    metrics.errors.push({
      timestamp: Date.now(),
      error
    });

    // 只保留最近的错误记录
    if (metrics.errors.length > 50) {
      metrics.errors = metrics.errors.slice(-50);
    }

    this.metrics.set(taskId, metrics);
  }

  /**
   * 完成任务监控
   */
  static endTask(taskId: string): void {
    const metrics = this.metrics.get(taskId);
    if (!metrics) return;

    metrics.endTime = Date.now();
    metrics.status = 'completed';
    
    const summary = this.generateSummary(taskId);
    console.log(`📊 Performance monitoring completed for task: ${taskId}`, summary);

    this.metrics.set(taskId, metrics);
  }

  /**
   * 获取任务性能摘要
   */
  static generateSummary(taskId: string): any {
    const metrics = this.metrics.get(taskId);
    if (!metrics) return null;

    const totalTime = (metrics.endTime || Date.now()) - metrics.startTime;
    const avgProcessingTime = metrics.processingTimes.length > 0 
      ? metrics.processingTimes.reduce((a: number, b: number) => a + b, 0) / metrics.processingTimes.length
      : 0;

    const throughput = metrics.completedItems > 0 
      ? (metrics.completedItems / (totalTime / 1000)) * 60 // items per minute
      : 0;

    return {
      taskId,
      totalItems: metrics.totalItems,
      completedItems: metrics.completedItems,
      failedItems: metrics.failedItems,
      successRate: metrics.totalItems > 0 ? (metrics.completedItems / metrics.totalItems) * 100 : 0,
      totalTime: totalTime,
      avgProcessingTime: Math.round(avgProcessingTime),
      throughput: Math.round(throughput * 100) / 100,
      memoryUsage: metrics.memoryUsage,
      errorCount: metrics.errors.length,
      status: metrics.status
    };
  }

  /**
   * 获取实时性能指标
   */
  static getRealTimeMetrics(taskId: string): any {
    const metrics = this.metrics.get(taskId);
    if (!metrics) return null;

    const currentTime = Date.now();
    const elapsedTime = currentTime - metrics.startTime;
    const recentProcessingTimes = metrics.processingTimes.slice(-10); // 最近10个
    
    const avgRecentProcessingTime = recentProcessingTimes.length > 0
      ? recentProcessingTimes.reduce((a: number, b: number) => a + b, 0) / recentProcessingTimes.length
      : 0;

    const estimatedTimeRemaining = avgRecentProcessingTime > 0 && metrics.completedItems > 0
      ? ((metrics.totalItems - metrics.completedItems) * avgRecentProcessingTime) / 1000
      : null;

    const currentThroughput = metrics.completedItems > 0 && elapsedTime > 0
      ? (metrics.completedItems / (elapsedTime / 1000)) * 60
      : 0;

    return {
      taskId,
      elapsedTime,
      completedItems: metrics.completedItems,
      failedItems: metrics.failedItems,
      remainingItems: metrics.totalItems - metrics.completedItems - metrics.failedItems,
      progress: Math.round(((metrics.completedItems + metrics.failedItems) / metrics.totalItems) * 100),
      avgRecentProcessingTime: Math.round(avgRecentProcessingTime),
      estimatedTimeRemaining: estimatedTimeRemaining ? Math.round(estimatedTimeRemaining) : null,
      currentThroughput: Math.round(currentThroughput * 100) / 100,
      memoryUsage: this.getMemoryUsage(),
      recentErrors: metrics.errors.slice(-5) // 最近5个错误
    };
  }

  /**
   * 获取性能建议
   */
  static getPerformanceRecommendations(taskId: string): string[] {
    const metrics = this.metrics.get(taskId);
    if (!metrics) return [];

    const recommendations: string[] = [];
    const summary = this.generateSummary(taskId);

    // 基于成功率的建议
    if (summary.successRate < 90) {
      recommendations.push('成功率较低，建议检查数据格式和网络连接');
    }

    // 基于处理速度的建议
    if (summary.avgProcessingTime > 5000) { // 超过5秒
      recommendations.push('处理速度较慢，建议减少并发数量或分批处理');
    }

    // 基于内存使用的建议
    if (summary.memoryUsage && summary.memoryUsage.usedJSHeapSize > 100 * 1024 * 1024) { // 超过100MB
      recommendations.push('内存使用较高，建议减少批量大小');
    }

    // 基于错误率的建议
    const errorRate = (summary.errorCount / summary.totalItems) * 100;
    if (errorRate > 10) {
      recommendations.push('错误率较高，建议检查数据质量');
    }

    // 基于吞吐量的建议
    if (summary.throughput < 10) { // 每分钟少于10个
      recommendations.push('处理效率较低，建议优化网络环境或减少数据复杂度');
    }

    return recommendations;
  }

  /**
   * 清理过期的监控数据
   */
  static cleanup(): void {
    const now = Date.now();
    const expiredTasks: string[] = [];

    this.metrics.forEach((metrics, taskId) => {
      const age = now - metrics.startTime;
      // 清理24小时前的数据
      if (age > 24 * 60 * 60 * 1000) {
        expiredTasks.push(taskId);
      }
    });

    expiredTasks.forEach(taskId => {
      this.metrics.delete(taskId);
    });

    if (expiredTasks.length > 0) {
      console.log(`🧹 Cleaned up ${expiredTasks.length} expired performance metrics`);
    }
  }

  /**
   * 获取内存使用情况
   */
  private static getMemoryUsage(): any {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in (window.performance as any)) {
      const memory = (window.performance as any).memory;
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit
      };
    }
    return null;
  }

  /**
   * 获取所有活跃任务的性能概览
   */
  static getOverview(): any {
    const activeTasks = Array.from(this.metrics.values()).filter(m => m.status === 'running');
    const completedTasks = Array.from(this.metrics.values()).filter(m => m.status === 'completed');

    return {
      activeTasks: activeTasks.length,
      completedTasks: completedTasks.length,
      totalItemsProcessed: completedTasks.reduce((sum, m) => sum + m.completedItems, 0),
      avgSuccessRate: completedTasks.length > 0 
        ? completedTasks.reduce((sum, m) => sum + (m.completedItems / m.totalItems), 0) / completedTasks.length * 100
        : 0,
      avgThroughput: completedTasks.length > 0
        ? completedTasks.reduce((sum, m) => {
            const totalTime = m.endTime - m.startTime;
            return sum + (m.completedItems / (totalTime / 1000)) * 60;
          }, 0) / completedTasks.length
        : 0
    };
  }
}
