'use client';

import React, { useState, useEffect, useRef, ReactNode } from 'react';

interface LazyLoadProps {
  children: ReactNode;
  fallback?: ReactNode;
  rootMargin?: string;
  threshold?: number;
  className?: string;
  once?: boolean;
}

export default function LazyLoad({
  children,
  fallback = <div className="animate-pulse bg-gray-200 rounded" />,
  rootMargin = '50px',
  threshold = 0.1,
  className,
  once = true,
}: LazyLoadProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // 如果已经加载过且设置了once，直接显示
    if (hasLoaded && once) {
      setIsVisible(true);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          setHasLoaded(true);
          
          // 如果设置了once，观察一次后就停止观察
          if (once) {
            observer.unobserve(element);
          }
        } else if (!once) {
          setIsVisible(false);
        }
      },
      {
        rootMargin,
        threshold,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [rootMargin, threshold, once, hasLoaded]);

  return (
    <div ref={elementRef} className={className}>
      {isVisible ? children : fallback}
    </div>
  );
}

// 预设的懒加载组件
export const LazySection = ({ children, className }: { children: ReactNode; className?: string }) => (
  <LazyLoad
    fallback={
      <div className={`animate-pulse bg-gray-100 rounded-lg ${className}`} style={{ minHeight: '200px' }} />
    }
    className={className}
  >
    {children}
  </LazyLoad>
);

export const LazyImage = ({ 
  src, 
  alt, 
  className,
  width,
  height 
}: { 
  src: string; 
  alt: string; 
  className?: string;
  width?: number;
  height?: number;
}) => (
  <LazyLoad
    fallback={
      <div 
        className={`animate-pulse bg-gray-200 rounded ${className}`}
        style={{ width, height }}
      />
    }
  >
    <img 
      src={src} 
      alt={alt} 
      className={className}
      width={width}
      height={height}
      loading="lazy"
    />
  </LazyLoad>
);

// Hook for lazy loading data
export const useLazyLoad = (callback: () => void, options?: IntersectionObserverInit) => {
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
          callback();
          observer.unobserve(element);
        }
      },
      {
        rootMargin: '50px',
        threshold: 0.1,
        ...options,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [callback, isVisible, options]);

  return { elementRef, isVisible };
};
