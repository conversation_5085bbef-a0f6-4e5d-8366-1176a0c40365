import React from 'react';
import { Metadata } from 'next';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import BulkCertificateGeneratorPage from '@/components/bulk/BulkCertificateGeneratorPage';

export const metadata: Metadata = {
  title: 'Bulk Certificate Generator - Create Multiple Professional Certificates | Free Online Tool',
  description: 'Generate hundreds of professional certificates in bulk using Excel or CSV files. Free bulk certificate generator with professional templates. Perfect for schools, organizations, and events.',
  keywords: [
    'bulk certificate generator',
    'bulk certificate creation',
    'mass certificate generation',
    'certificate batch processing',
    'Excel to certificates',
    'CSV certificate generator',
    'bulk PDF certificates',
    'certificate automation',
    'multiple certificates',
    'free certificate maker bulk',
    'professional certificate templates',
    'certificate generator online'
  ],
  openGraph: {
    title: 'Bulk Certificate Generator - Create Multiple Professional Certificates',
    description: 'Generate hundreds of professional certificates in bulk using Excel or CSV files. Free bulk certificate generator with professional templates.',
    type: 'website',
    url: '/bulk-certificate-generator',
    images: [
      {
        url: '/images/bulk-certificate-generator-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Bulk Certificate Generator - Create Multiple Certificates'
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Bulk Certificate Generator - Create Multiple Professional Certificates',
    description: 'Generate hundreds of professional certificates in bulk using Excel or CSV files. Free and professional.',
  },
  alternates: {
    canonical: '/bulk-certificate-generator',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  }
};

export default function BulkCertificateGeneratorPageRoute() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Bulk Certificate Generator",
    "description": "Generate hundreds of professional certificates in bulk using Excel or CSV files. Free bulk certificate generator with professional templates. Perfect for schools, organizations, and events.",
    "url": "https://certificatemaker.com/bulk-certificate-generator",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "Bulk certificate generation",
      "Excel and CSV file support",
      "Professional certificate templates",
      "High-quality PDF output",
      "ZIP file download",
      "Real-time progress tracking",
      "Free to use",
      "No registration required"
    ],
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "reviewCount": "1250"
    }
  };

  const faqStructuredData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "How many certificates can I generate at once with the bulk certificate generator?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "You can generate up to 1000 certificates in a single batch using our bulk certificate generator. For larger quantities, simply process multiple batches. The tool is completely free to use."
        }
      },
      {
        "@type": "Question",
        "name": "What file formats are supported for bulk certificate generation?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Our bulk certificate generator supports Excel files (.xlsx, .xls) and CSV files. Maximum file size is 10MB per upload. You can download sample templates to ensure proper formatting."
        }
      },
      {
        "@type": "Question",
        "name": "How long does bulk certificate generation take?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Most bulk certificate generation batches complete within 2-5 minutes. Processing time depends on the number of certificates and template complexity. You'll see real-time progress updates."
        }
      },
      {
        "@type": "Question",
        "name": "Is the bulk certificate generator free to use?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes, our bulk certificate generator is completely free to use. Generate as many professional certificates as you need at no cost. No registration or subscription required."
        }
      },
      {
        "@type": "Question",
        "name": "What certificate templates are available for bulk generation?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "We offer a wide variety of professional certificate templates including completion certificates, achievement awards, training certificates, and more. All templates are optimized for bulk generation."
        }
      }
    ]
  };

  const breadcrumbStructuredData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": "https://certificatemaker.com"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Bulk Certificate Generator",
        "item": "https://certificatemaker.com/bulk-certificate-generator"
      }
    ]
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqStructuredData) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbStructuredData) }}
      />
      <Header />
      <main className="min-h-screen" role="main">
        <article>
          <BulkCertificateGeneratorPage />
        </article>
      </main>
      <Footer />
    </>
  );
}
