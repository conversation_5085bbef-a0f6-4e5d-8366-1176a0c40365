'use client';

import React, { useState } from 'react';
import { 
  FileText, 
  Upload, 
  Eye, 
  Download, 
  CheckCircle,
  ChevronRight,
  ChevronDown,
  Info,
  AlertCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface BatchGuideProps {
  onStartBatch: () => void;
  className?: string;
}

export default function BatchGuide({ onStartBatch, className }: BatchGuideProps) {
  const [expandedStep, setExpandedStep] = useState<number | null>(null);

  const steps = [
    {
      id: 1,
      title: '选择证书模板',
      description: '从多种专业模板中选择适合的证书类型',
      icon: <FileText className="h-6 w-6 text-blue-600" />,
      details: [
        '浏览成就、完成、参与、优秀四大类证书模板',
        '每个分类都有多个精美设计可选',
        '支持横向和纵向两种布局',
        '所有模板都经过专业设计，确保打印质量'
      ],
      tips: '建议根据证书用途选择合适的分类，比如课程完成选择"完成证书"'
    },
    {
      id: 2,
      title: '准备数据文件',
      description: '按照指定格式准备Excel或CSV文件',
      icon: <Upload className="h-6 w-6 text-green-600" />,
      details: [
        '支持 .xlsx、.xls、.csv 三种格式',
        '文件大小不超过 10MB',
        '建议包含表头行，便于数据识别',
        '最多支持 1000 行数据'
      ],
      tips: '确保数据完整性，空白行会被自动跳过',
      format: {
        columns: [
          { name: 'A列', field: '收件人姓名', example: '张三', required: true },
          { name: 'B列', field: '日期', example: '2024-01-15', required: true },
          { name: 'C列', field: '签名', example: '李主任', required: true },
          { name: 'D列', field: '证书详情', example: '完成了高级项目管理课程', required: true }
        ]
      }
    },
    {
      id: 3,
      title: '上传并预览',
      description: '上传文件并检查解析结果',
      icon: <Eye className="h-6 w-6 text-purple-600" />,
      details: [
        '系统自动解析文件内容',
        '实时显示有效数据和错误信息',
        '支持数据预览和错误修正',
        '确认无误后即可开始生成'
      ],
      tips: '仔细检查预览数据，确保所有信息正确无误'
    },
    {
      id: 4,
      title: '批量生成',
      description: '系统自动生成所有证书PDF文件',
      icon: <Download className="h-6 w-6 text-orange-600" />,
      details: [
        '并行处理，提高生成效率',
        '实时显示生成进度',
        '自动处理错误和异常情况',
        '生成完成后自动打包为ZIP文件'
      ],
      tips: '生成过程中请保持网络连接稳定，避免关闭页面'
    }
  ];

  const toggleStep = (stepId: number) => {
    setExpandedStep(expandedStep === stepId ? null : stepId);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 介绍部分 */}
      <Card className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center">
              <FileText className="h-8 w-8 text-white" />
            </div>
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              批量生成证书
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              一次性生成多个证书，支持Excel和CSV文件导入，自动处理数据并生成高质量PDF证书文件
            </p>
          </div>
          <div className="flex justify-center space-x-4">
            <Badge variant="secondary" className="px-3 py-1">
              <CheckCircle className="h-4 w-4 mr-1" />
              最多1000个证书
            </Badge>
            <Badge variant="secondary" className="px-3 py-1">
              <CheckCircle className="h-4 w-4 mr-1" />
              支持多种格式
            </Badge>
            <Badge variant="secondary" className="px-3 py-1">
              <CheckCircle className="h-4 w-4 mr-1" />
              自动打包下载
            </Badge>
          </div>
        </div>
      </Card>

      {/* 步骤指南 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">操作步骤</h3>
        
        {steps.map((step, index) => (
          <Card key={step.id} className="overflow-hidden">
            <div
              className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
              onClick={() => toggleStep(step.id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {step.icon}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">
                      步骤 {step.id}: {step.title}
                    </h4>
                    <p className="text-sm text-gray-600">{step.description}</p>
                  </div>
                </div>
                <div className="flex-shrink-0">
                  {expandedStep === step.id ? (
                    <ChevronDown className="h-5 w-5 text-gray-400" />
                  ) : (
                    <ChevronRight className="h-5 w-5 text-gray-400" />
                  )}
                </div>
              </div>
            </div>

            {expandedStep === step.id && (
              <div className="px-4 pb-4 border-t border-gray-100">
                <div className="pt-4 space-y-4">
                  {/* 详细说明 */}
                  <div>
                    <h5 className="font-medium text-gray-900 mb-2">详细说明</h5>
                    <ul className="space-y-1">
                      {step.details.map((detail, idx) => (
                        <li key={idx} className="flex items-start space-x-2 text-sm text-gray-600">
                          <span className="flex-shrink-0 w-1.5 h-1.5 bg-blue-600 rounded-full mt-2"></span>
                          <span>{detail}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* 数据格式说明 */}
                  {step.format && (
                    <div>
                      <h5 className="font-medium text-gray-900 mb-2">数据格式要求</h5>
                      <div className="bg-gray-50 rounded-lg p-3">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          {step.format.columns.map((col, idx) => (
                            <div key={idx} className="flex items-center space-x-3">
                              <Badge variant="outline" className="text-xs">
                                {col.name}
                              </Badge>
                              <span className="text-sm font-medium text-gray-900">
                                {col.field}
                              </span>
                              {col.required && (
                                <span className="text-xs text-red-600">*必填</span>
                              )}
                            </div>
                          ))}
                        </div>
                        <div className="mt-3 pt-3 border-t border-gray-200">
                          <p className="text-xs text-gray-600">
                            示例数据：张三 | 2024-01-15 | 李主任 | 完成了高级项目管理课程
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 提示信息 */}
                  {step.tips && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <div className="flex items-start space-x-2">
                        <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                        <p className="text-sm text-blue-800">{step.tips}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </Card>
        ))}
      </div>

      {/* 注意事项 */}
      <Card className="p-4 bg-yellow-50 border-yellow-200">
        <div className="flex items-start space-x-2">
          <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
          <div className="space-y-2">
            <h4 className="font-medium text-yellow-900">注意事项</h4>
            <ul className="text-sm text-yellow-800 space-y-1">
              <li>• 生成过程中请保持网络连接稳定</li>
              <li>• 大批量数据建议分批处理，避免超时</li>
              <li>• 生成的文件会在24小时后自动清理</li>
              <li>• 如遇到问题，请检查数据格式或联系技术支持</li>
            </ul>
          </div>
        </div>
      </Card>

      {/* 开始按钮 */}
      <div className="text-center pt-4">
        <Button
          onClick={onStartBatch}
          size="lg"
          className="bg-blue-600 hover:bg-blue-700 px-8 py-4 text-lg"
        >
          开始批量生成
          <ChevronRight className="h-5 w-5 ml-2" />
        </Button>
      </div>
    </div>
  );
}
