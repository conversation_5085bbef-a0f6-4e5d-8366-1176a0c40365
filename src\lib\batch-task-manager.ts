import { 
  BatchGenerationTask, 
  BatchTaskStatus, 
  BatchGenerationError,
  BatchGenerationProgress,
  BatchCertificateData 
} from '@/types/certificate';

/**
 * 批量任务管理器
 * 负责管理批量生成任务的生命周期
 */
export class BatchTaskManager {
  private static tasks = new Map<string, BatchGenerationTask>();
  private static readonly TASK_CLEANUP_DELAY = 24 * 60 * 60 * 1000; // 24小时后清理任务

  /**
   * 创建新的批量任务
   */
  static createTask(
    templateId: string,
    certificates: BatchCertificateData[]
  ): BatchGenerationTask {
    const taskId = this.generateTaskId();
    const now = new Date();

    const task: BatchGenerationTask = {
      id: taskId,
      templateId,
      totalCount: certificates.length,
      completedCount: 0,
      failedCount: 0,
      status: BatchTaskStatus.PENDING,
      createdAt: now,
      updatedAt: now,
      errors: []
    };

    this.tasks.set(taskId, task);
    
    // 设置自动清理
    setTimeout(() => {
      this.cleanupTask(taskId);
    }, this.TASK_CLEANUP_DELAY);

    return task;
  }

  /**
   * 获取任务信息
   */
  static getTask(taskId: string): BatchGenerationTask | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * 更新任务状态
   */
  static updateTaskStatus(taskId: string, status: BatchTaskStatus): void {
    const task = this.tasks.get(taskId);
    if (task) {
      task.status = status;
      task.updatedAt = new Date();
      this.tasks.set(taskId, task);
    }
  }

  /**
   * 更新任务进度
   */
  static updateTaskProgress(
    taskId: string,
    completedCount: number,
    failedCount: number = 0
  ): void {
    const task = this.tasks.get(taskId);
    if (task) {
      task.completedCount = completedCount;
      task.failedCount = failedCount;
      task.updatedAt = new Date();
      
      // 如果所有项目都已处理完成，更新状态
      if (completedCount + failedCount >= task.totalCount) {
        task.status = failedCount > 0 ? BatchTaskStatus.COMPLETED : BatchTaskStatus.COMPLETED;
      }
      
      this.tasks.set(taskId, task);
    }
  }

  /**
   * 添加错误信息
   */
  static addTaskError(taskId: string, error: BatchGenerationError): void {
    const task = this.tasks.get(taskId);
    if (task) {
      task.errors.push(error);
      task.updatedAt = new Date();
      this.tasks.set(taskId, task);
    }
  }

  /**
   * 设置下载链接
   */
  static setDownloadUrl(taskId: string, downloadUrl: string): void {
    const task = this.tasks.get(taskId);
    if (task) {
      task.downloadUrl = downloadUrl;
      task.updatedAt = new Date();
      this.tasks.set(taskId, task);
    }
  }

  /**
   * 取消任务
   */
  static cancelTask(taskId: string): boolean {
    const task = this.tasks.get(taskId);
    if (task && task.status === BatchTaskStatus.PENDING || task.status === BatchTaskStatus.PROCESSING) {
      task.status = BatchTaskStatus.CANCELLED;
      task.updatedAt = new Date();
      this.tasks.set(taskId, task);
      return true;
    }
    return false;
  }

  /**
   * 获取任务进度信息
   */
  static getTaskProgress(taskId: string): BatchGenerationProgress | null {
    const task = this.tasks.get(taskId);
    if (!task) {
      return null;
    }

    const progress = task.totalCount > 0 
      ? Math.round(((task.completedCount + task.failedCount) / task.totalCount) * 100)
      : 0;

    return {
      taskId: task.id,
      status: task.status,
      totalCount: task.totalCount,
      completedCount: task.completedCount,
      failedCount: task.failedCount,
      progress,
      errors: task.errors,
      estimatedTimeRemaining: this.calculateEstimatedTime(task)
    };
  }

  /**
   * 清理过期任务
   */
  private static cleanupTask(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (task && (
      task.status === BatchTaskStatus.COMPLETED ||
      task.status === BatchTaskStatus.FAILED ||
      task.status === BatchTaskStatus.CANCELLED
    )) {
      this.tasks.delete(taskId);
      console.log(`Cleaned up expired task: ${taskId}`);
    }
  }

  /**
   * 生成唯一任务ID
   */
  private static generateTaskId(): string {
    const timestamp = Date.now().toString(36);
    const randomStr = Math.random().toString(36).substring(2, 8);
    return `batch_${timestamp}_${randomStr}`;
  }

  /**
   * 计算预估剩余时间（秒）
   */
  private static calculateEstimatedTime(task: BatchGenerationTask): number | undefined {
    if (task.status !== BatchTaskStatus.PROCESSING || task.completedCount === 0) {
      return undefined;
    }

    const elapsedTime = Date.now() - task.createdAt.getTime();
    const avgTimePerItem = elapsedTime / task.completedCount;
    const remainingItems = task.totalCount - task.completedCount - task.failedCount;
    
    return Math.round((avgTimePerItem * remainingItems) / 1000);
  }

  /**
   * 获取所有活跃任务
   */
  static getActiveTasks(): BatchGenerationTask[] {
    return Array.from(this.tasks.values()).filter(task => 
      task.status === BatchTaskStatus.PENDING || 
      task.status === BatchTaskStatus.PROCESSING
    );
  }

  /**
   * 获取任务统计信息
   */
  static getTaskStats(): {
    total: number;
    pending: number;
    processing: number;
    completed: number;
    failed: number;
    cancelled: number;
  } {
    const tasks = Array.from(this.tasks.values());
    
    return {
      total: tasks.length,
      pending: tasks.filter(t => t.status === BatchTaskStatus.PENDING).length,
      processing: tasks.filter(t => t.status === BatchTaskStatus.PROCESSING).length,
      completed: tasks.filter(t => t.status === BatchTaskStatus.COMPLETED).length,
      failed: tasks.filter(t => t.status === BatchTaskStatus.FAILED).length,
      cancelled: tasks.filter(t => t.status === BatchTaskStatus.CANCELLED).length
    };
  }
}
