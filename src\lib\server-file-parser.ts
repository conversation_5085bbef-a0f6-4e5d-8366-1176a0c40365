import * as XLSX from 'xlsx';
import Papa from 'papaparse';
import { 
  BatchCertificateData, 
  FileUploadResult, 
  FileParseError, 
  FileParseOptions,
  ColumnMapping 
} from '@/types/certificate';

/**
 * 服务器端文件解析工具类
 * 支持Excel和CSV文件的解析和验证
 */
export class ServerFileParser {
  private static readonly SUPPORTED_EXTENSIONS = ['.xlsx', '.xls', '.csv'];
  private static readonly MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  private static readonly MAX_ROWS = 1000; // 最大行数限制

  /**
   * 验证文件是否符合要求
   */
  static validateFile(file: File): { valid: boolean; error?: string } {
    // 检查文件大小
    if (file.size > this.MAX_FILE_SIZE) {
      return {
        valid: false,
        error: `文件大小不能超过 ${this.MAX_FILE_SIZE / 1024 / 1024}MB`
      };
    }

    // 检查文件扩展名
    const extension = this.getFileExtension(file.name);
    if (!this.SUPPORTED_EXTENSIONS.includes(extension)) {
      return {
        valid: false,
        error: `不支持的文件格式。支持的格式：${this.SUPPORTED_EXTENSIONS.join(', ')}`
      };
    }

    return { valid: true };
  }

  /**
   * 解析文件内容
   */
  static async parseFile(
    file: File, 
    options: FileParseOptions = {}
  ): Promise<FileUploadResult> {
    try {
      // 验证文件
      const validation = this.validateFile(file);
      if (!validation.valid) {
        return {
          success: false,
          errors: [{ row: 0, message: validation.error! }]
        };
      }

      const extension = this.getFileExtension(file.name);
      let rawData: any[][];

      // 根据文件类型解析
      if (extension === '.csv') {
        rawData = await this.parseCSV(file);
      } else {
        rawData = await this.parseExcel(file);
      }

      // 处理解析后的数据
      return this.processRawData(rawData, options);

    } catch (error) {
      console.error('文件解析错误:', error);
      return {
        success: false,
        errors: [{ 
          row: 0, 
          message: error instanceof Error ? error.message : '文件解析失败'
        }]
      };
    }
  }

  /**
   * 解析CSV文件
   */
  private static async parseCSV(file: File): Promise<any[][]> {
    try {
      // 在服务器端，使用 arrayBuffer() 方法读取文件
      const arrayBuffer = await file.arrayBuffer();
      const text = new TextDecoder('utf-8').decode(arrayBuffer);

      return new Promise((resolve, reject) => {
        Papa.parse(text, {
          complete: (results) => {
            if (results.errors.length > 0) {
              reject(new Error(`CSV解析错误: ${results.errors[0].message}`));
            } else {
              resolve(results.data as any[][]);
            }
          },
          error: (error) => {
            reject(new Error(`CSV解析失败: ${error.message}`));
          },
          skipEmptyLines: true
        });
      });
    } catch (error) {
      throw new Error(`CSV文件读取失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 解析Excel文件
   */
  private static async parseExcel(file: File): Promise<any[][]> {
    try {
      // 在服务器端，使用 arrayBuffer() 方法读取文件
      const arrayBuffer = await file.arrayBuffer();
      const data = new Uint8Array(arrayBuffer);
      const workbook = XLSX.read(data, { type: 'array' });

      // 获取第一个工作表
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];

      // 转换为二维数组
      const jsonData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
        defval: ''
      }) as any[][];

      return jsonData;
    } catch (error) {
      throw new Error(`Excel解析失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 处理原始数据
   */
  private static processRawData(
    rawData: any[][],
    options: FileParseOptions
  ): FileUploadResult {
    const errors: FileParseError[] = [];
    const certificates: BatchCertificateData[] = [];
    
    // 检查数据是否为空
    if (!rawData || rawData.length === 0) {
      return {
        success: false,
        errors: [{ row: 0, message: '文件内容为空' }]
      };
    }

    // 检查行数限制
    if (rawData.length > this.MAX_ROWS) {
      return {
        success: false,
        errors: [{ 
          row: 0, 
          message: `数据行数超过限制，最多支持 ${this.MAX_ROWS} 行数据` 
        }]
      };
    }

    // 确定数据开始行
    const startRow = options.hasHeader !== false ? 1 : 0;
    const columnMapping = options.columnMapping || this.getDefaultColumnMapping();

    // 处理每一行数据
    for (let i = startRow; i < rawData.length; i++) {
      const row = rawData[i];
      
      // 跳过空行
      if (options.skipEmptyRows !== false && this.isEmptyRow(row)) {
        continue;
      }

      try {
        const certificate = this.parseRowToCertificate(row, columnMapping, i + 1);
        
        // 验证证书数据
        const validationErrors = this.validateCertificateData(certificate, i + 1);
        if (validationErrors.length > 0) {
          errors.push(...validationErrors);
        } else {
          certificate.rowIndex = i + 1;
          certificates.push(certificate);
        }
      } catch (error) {
        errors.push({
          row: i + 1,
          message: error instanceof Error ? error.message : '行数据解析失败'
        });
      }
    }

    return {
      success: errors.length === 0,
      data: certificates,
      errors: errors.length > 0 ? errors : undefined,
      totalRows: rawData.length - startRow,
      validRows: certificates.length
    };
  }

  /**
   * 将行数据转换为证书数据
   */
  private static parseRowToCertificate(
    row: any[],
    mapping: ColumnMapping,
    rowNumber: number
  ): BatchCertificateData {
    // 获取列索引
    const nameIndex = this.getColumnIndex(mapping.recipientName);
    const dateIndex = this.getColumnIndex(mapping.date);
    const signatureIndex = this.getColumnIndex(mapping.signature);
    const detailsIndex = this.getColumnIndex(mapping.details);

    return {
      recipientName: this.getCellValue(row, nameIndex),
      date: this.getCellValue(row, dateIndex),
      signature: this.getCellValue(row, signatureIndex),
      details: this.getCellValue(row, detailsIndex)
    };
  }

  /**
   * 验证证书数据
   */
  private static validateCertificateData(
    data: BatchCertificateData,
    rowNumber: number
  ): FileParseError[] {
    const errors: FileParseError[] = [];

    // 验证必填字段
    if (!data.recipientName?.trim()) {
      errors.push({
        row: rowNumber,
        column: 'recipientName',
        message: '收件人姓名不能为空',
        value: data.recipientName
      });
    }

    if (!data.date?.trim()) {
      errors.push({
        row: rowNumber,
        column: 'date',
        message: '日期不能为空',
        value: data.date
      });
    }

    if (!data.signature?.trim()) {
      errors.push({
        row: rowNumber,
        column: 'signature',
        message: '签名不能为空',
        value: data.signature
      });
    }

    if (!data.details?.trim()) {
      errors.push({
        row: rowNumber,
        column: 'details',
        message: '证书详情不能为空',
        value: data.details
      });
    }

    // 验证字段长度
    if (data.recipientName && data.recipientName.length > 50) {
      errors.push({
        row: rowNumber,
        column: 'recipientName',
        message: '收件人姓名不能超过50个字符',
        value: data.recipientName
      });
    }

    if (data.details && data.details.length > 200) {
      errors.push({
        row: rowNumber,
        column: 'details',
        message: '证书详情不能超过200个字符',
        value: data.details
      });
    }

    return errors;
  }

  /**
   * 获取默认列映射
   */
  private static getDefaultColumnMapping(): ColumnMapping {
    return {
      recipientName: 'A', // 第一列：收件人姓名
      date: 'B',          // 第二列：日期
      signature: 'C',     // 第三列：签名
      details: 'D'        // 第四列：详情
    };
  }

  /**
   * 获取文件扩展名
   */
  private static getFileExtension(filename: string): string {
    return filename.toLowerCase().substring(filename.lastIndexOf('.'));
  }

  /**
   * 检查是否为空行
   */
  private static isEmptyRow(row: any[]): boolean {
    return !row || row.every(cell => !cell || cell.toString().trim() === '');
  }

  /**
   * 获取列索引
   */
  private static getColumnIndex(column: string): number {
    // 支持数字索引和字母索引
    if (/^\d+$/.test(column)) {
      return parseInt(column) - 1; // 1-based to 0-based
    }
    
    // 字母索引转数字 (A=0, B=1, etc.)
    let index = 0;
    for (let i = 0; i < column.length; i++) {
      index = index * 26 + (column.charCodeAt(i) - 'A'.charCodeAt(0) + 1);
    }
    return index - 1;
  }

  /**
   * 获取单元格值
   */
  private static getCellValue(row: any[], index: number): string {
    const value = row[index];
    return value ? value.toString().trim() : '';
  }
}
