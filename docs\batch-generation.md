# 批量生成证书功能文档

## 功能概述

批量生成证书功能允许用户通过上传Excel或CSV文件，一次性生成多个证书PDF文件。该功能特别适合学校、培训机构、企业等需要大量生成证书的场景。

## 主要特性

- **多格式支持**: 支持 .xlsx、.xls、.csv 三种文件格式
- **大批量处理**: 单次最多支持1000个证书生成
- **实时进度**: 提供实时进度监控和状态反馈
- **错误处理**: 智能错误检测和详细错误报告
- **自动打包**: 生成完成后自动打包为ZIP文件下载
- **性能优化**: 并行处理和内存优化，确保高效生成

## 使用流程

### 1. 选择证书模板
- 从四大分类中选择合适的证书模板：
  - 成就证书：用于表彰特定成就和里程碑
  - 完成证书：用于确认课程或项目的完成
  - 参与证书：用于确认活动或事件的参与
  - 优秀证书：用于表彰卓越表现和杰出贡献

### 2. 准备数据文件
按照以下格式准备数据文件：

| 列 | 字段名 | 说明 | 示例 | 必填 |
|---|--------|------|------|------|
| A | 收件人姓名 | 证书接收者的姓名 | 张三 | ✓ |
| B | 日期 | 证书颁发日期 | 2024-01-15 | ✓ |
| C | 签名 | 签发人或机构名称 | 李主任 | ✓ |
| D | 证书详情 | 证书的具体内容描述 | 完成了高级项目管理课程 | ✓ |

**文件要求：**
- 文件大小不超过10MB
- 最多1000行数据
- 建议包含表头行
- 支持中文和英文内容

### 3. 上传文件
- 拖拽文件到上传区域或点击选择文件
- 系统自动解析文件内容
- 实时显示解析结果和错误信息

### 4. 预览数据
- 查看解析后的数据预览
- 检查错误信息并确认数据正确性
- 确认无误后开始生成

### 5. 批量生成
- 系统并行处理所有证书
- 实时显示生成进度
- 自动处理错误和异常情况

### 6. 下载结果
- 生成完成后自动打包为ZIP文件
- 点击下载按钮获取所有证书

## API接口

### 文件上传接口
```
POST /api/batch/upload
Content-Type: multipart/form-data

参数:
- file: 上传的文件
- hasHeader: 是否包含表头 (可选)
- skipEmptyRows: 是否跳过空行 (可选)
```

### 批量生成接口
```
POST /api/batch/generate
Content-Type: application/json

{
  "templateId": "模板ID",
  "certificates": [
    {
      "recipientName": "收件人姓名",
      "date": "日期",
      "signature": "签名",
      "details": "详情"
    }
  ],
  "options": {
    "parallel": true,
    "maxConcurrency": 3,
    "outputFormat": "zip"
  }
}
```

### 进度查询接口
```
GET /api/batch/progress/{taskId}

返回:
{
  "success": true,
  "progress": {
    "taskId": "任务ID",
    "status": "processing",
    "totalCount": 100,
    "completedCount": 50,
    "failedCount": 2,
    "progress": 52,
    "errors": []
  }
}
```

### 下载接口
```
GET /api/batch/download/{taskId}

返回: ZIP文件流
```

## 性能优化

### 并发处理
- 默认并发数：3
- 可根据服务器性能调整
- 自动负载均衡

### 内存管理
- 流式处理大文件
- 及时清理临时文件
- 内存使用监控

### 错误恢复
- 自动重试机制
- 详细错误日志
- 部分失败处理

## 错误处理

### 常见错误类型
1. **文件格式错误**: 不支持的文件格式
2. **文件大小超限**: 文件超过10MB限制
3. **数据格式错误**: 必填字段缺失或格式不正确
4. **网络错误**: 上传或生成过程中的网络问题
5. **服务器错误**: 服务器内部错误

### 错误解决方案
- 检查文件格式和大小
- 验证数据完整性
- 确保网络连接稳定
- 联系技术支持

## 最佳实践

### 数据准备
1. 使用标准的Excel或CSV格式
2. 确保所有必填字段都已填写
3. 避免使用特殊字符
4. 保持数据格式一致

### 性能优化
1. 大批量数据建议分批处理
2. 避免在生成过程中关闭页面
3. 确保网络连接稳定
4. 定期清理浏览器缓存

### 安全考虑
1. 不要上传包含敏感信息的文件
2. 生成的文件会在24小时后自动清理
3. 建议及时下载生成的证书

## 技术架构

### 前端组件
- `BatchCertificateMaker`: 主要的批量生成组件
- `FileUploadZone`: 文件上传组件
- `DataPreview`: 数据预览组件
- `ProgressMonitor`: 进度监控组件
- `TemplateSelector`: 模板选择组件

### 后端服务
- `FileParser`: 文件解析服务
- `BatchPDFGenerator`: 批量PDF生成器
- `BatchTaskManager`: 任务管理器
- `ZipPackager`: ZIP打包工具
- `BatchPerformanceMonitor`: 性能监控工具

### 数据流
1. 文件上传 → 解析验证 → 数据预览
2. 确认生成 → 创建任务 → 并行处理
3. 进度监控 → 错误处理 → 结果打包
4. 下载链接 → 文件清理

## 故障排除

### 常见问题
1. **上传失败**: 检查文件格式和大小
2. **解析错误**: 验证数据格式
3. **生成缓慢**: 减少并发数或分批处理
4. **下载失败**: 检查网络连接

### 联系支持
如遇到技术问题，请提供：
- 错误信息截图
- 使用的文件格式和大小
- 浏览器版本信息
- 操作步骤描述

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 支持Excel和CSV文件上传
- 实现批量PDF生成
- 添加进度监控和错误处理
- 集成性能监控工具
