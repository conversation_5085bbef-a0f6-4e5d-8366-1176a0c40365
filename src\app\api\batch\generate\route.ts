import { NextRequest, NextResponse } from 'next/server';
import { 
  BatchGenerationRequest,
  BatchGenerationResult,
  BatchTaskStatus
} from '@/types/certificate';
import { TemplateManager } from '@/lib/template-manager';
import { BatchTaskManager } from '@/lib/batch-task-manager';
import { BatchPDFGenerator } from '@/lib/batch-pdf-generator';
import { ZipPackager } from '@/lib/zip-packager';
import path from 'path';

/**
 * 批量PDF生成API
 * POST /api/batch/generate
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Batch PDF generation request received');

    const body: BatchGenerationRequest = await request.json();
    
    // 验证请求数据
    if (!body.templateId || !body.certificates || body.certificates.length === 0) {
      return NextResponse.json(
        { error: '请求数据无效' },
        { status: 400 }
      );
    }

    // 验证模板是否存在
    const template = TemplateManager.getTemplateById(body.templateId);
    if (!template) {
      return NextResponse.json(
        { error: '模板不存在' },
        { status: 404 }
      );
    }

    // 检查证书数量限制
    const maxCertificates = 1000;
    if (body.certificates.length > maxCertificates) {
      return NextResponse.json(
        { error: `证书数量不能超过 ${maxCertificates} 个` },
        { status: 400 }
      );
    }

    console.log(`📊 Generating ${body.certificates.length} certificates using template: ${template.displayName}`);

    // 创建批量任务
    const task = BatchTaskManager.createTask(body.templateId, body.certificates);
    
    // 异步处理生成任务
    processGenerationTask(task.id, template, body.certificates, body.options)
      .catch(error => {
        console.error(`❌ Batch generation task ${task.id} failed:`, error);
        BatchTaskManager.updateTaskStatus(task.id, BatchTaskStatus.FAILED);
      });

    // 立即返回任务ID
    return NextResponse.json({
      success: true,
      taskId: task.id,
      message: '批量生成任务已启动',
      totalCount: body.certificates.length
    });

  } catch (error) {
    console.error('❌ Batch generation API error:', error);
    
    return NextResponse.json(
      { 
        error: '服务器内部错误',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

/**
 * 处理生成任务（异步）
 */
async function processGenerationTask(
  taskId: string,
  template: any,
  certificates: any[],
  options: any = {}
) {
  try {
    console.log(`🔄 Processing generation task: ${taskId}`);

    // 创建PDF生成器
    const generator = new BatchPDFGenerator(taskId, template, certificates, {
      maxConcurrency: options?.maxConcurrency || 3
    });

    // 生成PDF文件
    const result = await generator.generate();

    if (result.success && result.generatedFiles.length > 0) {
      // 打包为ZIP文件
      const outputDir = generator.getOutputDirectory();
      const zipFilename = ZipPackager.generateZipFilename(`certificates_${taskId}`);
      const zipPath = path.join(outputDir, zipFilename);

      console.log(`📦 Creating ZIP package: ${zipPath}`);

      const zipResult = await ZipPackager.packDirectory(outputDir, zipPath, {
        fileFilter: (filename) => filename.endsWith('.pdf')
      });

      if (zipResult.success && zipResult.zipPath) {
        // 设置下载链接
        const downloadUrl = `/api/batch/download/${taskId}`;
        BatchTaskManager.setDownloadUrl(taskId, downloadUrl);
        
        console.log(`✅ Batch generation completed successfully: ${taskId}`);
        console.log(`📊 Generated ${result.generatedFiles.length} PDFs, ${result.errors.length} errors`);
      } else {
        throw new Error(`ZIP打包失败: ${zipResult.error}`);
      }
    } else {
      throw new Error('没有成功生成任何PDF文件');
    }

  } catch (error) {
    console.error(`❌ Generation task ${taskId} failed:`, error);
    BatchTaskManager.updateTaskStatus(taskId, BatchTaskStatus.FAILED);
    throw error;
  }
}

/**
 * 获取批量生成配置
 * GET /api/batch/generate
 */
export async function GET() {
  try {
    return NextResponse.json({
      config: {
        maxCertificates: 1000,
        maxConcurrency: 5,
        supportedTemplates: TemplateManager.getAllTemplates().map(t => ({
          id: t.id,
          name: t.displayName,
          category: t.category
        })),
        defaultOptions: {
          parallel: true,
          maxConcurrency: 3,
          outputFormat: 'zip'
        }
      },
      stats: BatchTaskManager.getTaskStats()
    });

  } catch (error) {
    console.error('❌ Get batch config error:', error);
    
    return NextResponse.json(
      { error: '获取配置失败' },
      { status: 500 }
    );
  }
}
