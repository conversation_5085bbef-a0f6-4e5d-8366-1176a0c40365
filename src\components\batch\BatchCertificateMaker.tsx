'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { ArrowLeft, Download, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { TemplateManager } from '@/lib/template-manager';
import { 
  CertificateTemplate, 
  BatchCertificateData, 
  FileUploadResult,
  BatchGenerationRequest
} from '@/types/certificate';

import TemplateSelector from './TemplateSelector';
import FileUploadZone from './FileUploadZone';
import DataPreview from './DataPreview';
import ProgressMonitor from './ProgressMonitor';
import BulkCertificateLanding from './BulkCertificateLanding';

enum BatchStep {
  LANDING = 'landing',
  TEMPLATE_SELECTION = 'template',
  FILE_UPLOAD = 'upload',
  DATA_PREVIEW = 'preview',
  GENERATION = 'generation',
  COMPLETED = 'completed'
}

export default function BatchCertificateMaker() {
  const { toast } = useToast();
  const searchParams = useSearchParams();

  // 状态管理
  const [currentStep, setCurrentStep] = useState<BatchStep>(BatchStep.LANDING);
  const [selectedTemplate, setSelectedTemplate] = useState<CertificateTemplate | null>(null);
  const [uploadResult, setUploadResult] = useState<FileUploadResult | null>(null);
  const [uploadedFileName, setUploadedFileName] = useState<string>('');
  const [taskId, setTaskId] = useState<string | null>(null);
  const [downloadUrl, setDownloadUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Check for pre-selected template from URL
  useEffect(() => {
    const templateId = searchParams.get('template');
    if (templateId) {
      const template = TemplateManager.getTemplateById(templateId);
      if (template) {
        setSelectedTemplate(template);
        setCurrentStep(BatchStep.FILE_UPLOAD);
      }
    }
  }, [searchParams]);

  // Get pre-selected category if template is selected
  const getPreSelectedCategory = useCallback(() => {
    if (selectedTemplate) {
      return selectedTemplate.category;
    }
    const templateId = searchParams.get('template');
    if (templateId) {
      const template = TemplateManager.getTemplateById(templateId);
      return template?.category;
    }
    return undefined;
  }, [selectedTemplate, searchParams]);

  // Start bulk generation
  const handleGetStarted = useCallback(() => {
    setCurrentStep(BatchStep.TEMPLATE_SELECTION);
  }, []);

  // 模板选择
  const handleTemplateSelect = useCallback((template: CertificateTemplate) => {
    setSelectedTemplate(template);
    setCurrentStep(BatchStep.FILE_UPLOAD);
  }, []);

  // 文件上传成功
  const handleFileUploaded = useCallback((result: FileUploadResult, fileName: string) => {
    setUploadResult(result);
    setUploadedFileName(fileName);
    setCurrentStep(BatchStep.DATA_PREVIEW);

    toast({
      title: "File uploaded successfully",
      description: `Parsed ${result.validRows} valid records`,
    });
  }, [toast]);

  // 文件上传错误
  const handleFileUploadError = useCallback((error: string) => {
    toast({
      title: "File upload failed",
      description: error,
      variant: "destructive",
    });
  }, [toast]);

  // 确认生成
  const handleConfirmGeneration = useCallback(async () => {
    if (!selectedTemplate || !uploadResult?.data) {
      toast({
        title: "Incomplete data",
        description: "Please ensure you have selected a template and uploaded valid data",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      const request: BatchGenerationRequest = {
        templateId: selectedTemplate.id,
        certificates: uploadResult.data,
        options: {
          parallel: true,
          maxConcurrency: 3,
          outputFormat: 'zip'
        }
      };

      const response = await fetch('/api/batch/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || '启动批量生成失败');
      }

      setTaskId(result.taskId);
      setCurrentStep(BatchStep.GENERATION);

      toast({
        title: "Bulk generation started",
        description: `Generating ${uploadResult.validRows} certificates`,
      });

    } catch (error) {
      console.error('Batch generation error:', error);
      toast({
        title: "Failed to start",
        description: error instanceof Error ? error.message : 'Failed to start bulk generation',
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [selectedTemplate, uploadResult, toast]);

  // 生成完成
  const handleGenerationComplete = useCallback((url: string) => {
    setDownloadUrl(url);
    setCurrentStep(BatchStep.COMPLETED);
    
    toast({
      title: "Generation complete",
      description: "All certificates have been generated and are ready for download",
    });
  }, [toast]);

  // 生成错误
  const handleGenerationError = useCallback((error: string) => {
    toast({
      title: "Generation failed",
      description: error,
      variant: "destructive",
    });
  }, [toast]);

  // 取消生成
  const handleGenerationCancel = useCallback(() => {
    setCurrentStep(BatchStep.DATA_PREVIEW);
    setTaskId(null);
    
    toast({
      title: "Task cancelled",
      description: "Bulk generation task has been cancelled",
    });
  }, [toast]);

  // 下载文件
  const handleDownload = useCallback(() => {
    if (downloadUrl) {
      window.open(downloadUrl, '_blank');
    }
  }, [downloadUrl]);

  // 重新开始
  const handleRestart = useCallback(() => {
    setCurrentStep(BatchStep.LANDING);
    setSelectedTemplate(null);
    setUploadResult(null);
    setTaskId(null);
    setDownloadUrl(null);
    setIsLoading(false);
  }, []);

  // Go back to previous step
  const handleGoBack = useCallback(() => {
    switch (currentStep) {
      case BatchStep.TEMPLATE_SELECTION:
        setCurrentStep(BatchStep.LANDING);
        break;
      case BatchStep.FILE_UPLOAD:
        setCurrentStep(BatchStep.TEMPLATE_SELECTION);
        break;
      case BatchStep.DATA_PREVIEW:
        setCurrentStep(BatchStep.FILE_UPLOAD);
        setUploadResult(null);
        break;
      case BatchStep.GENERATION:
        // Cannot go back during generation
        break;
      case BatchStep.COMPLETED:
        setCurrentStep(BatchStep.DATA_PREVIEW);
        setDownloadUrl(null);
        break;
    }
  }, [currentStep]);

  // Get step title and description - 保持静态标题用于SEO
  const getStepInfo = () => {
    const baseTitle = 'Bulk Certificate Generator';
    const baseDescription = 'Generate hundreds of professional certificates in minutes using Excel or CSV files';

    switch (currentStep) {
      case BatchStep.LANDING:
        return {
          title: baseTitle,
          description: baseDescription
        };
      case BatchStep.TEMPLATE_SELECTION:
        return {
          title: baseTitle,
          description: 'Step 1: Choose from our professional certificate templates'
        };
      case BatchStep.FILE_UPLOAD:
        return {
          title: baseTitle,
          description: 'Step 2: Upload an Excel or CSV file with recipient information'
        };
      case BatchStep.DATA_PREVIEW:
        return {
          title: baseTitle,
          description: 'Step 3: Review your data before generating certificates'
        };
      case BatchStep.GENERATION:
        return {
          title: baseTitle,
          description: 'Step 4: Generating your certificates - please wait'
        };
      case BatchStep.COMPLETED:
        return {
          title: baseTitle,
          description: 'Step 5: Your certificates are ready for download'
        };
      default:
        return {
          title: baseTitle,
          description: baseDescription
        };
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {currentStep === BatchStep.LANDING ? (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <BulkCertificateLanding onGetStarted={handleGetStarted} />
        </div>
      ) : (
        <div className="py-12 min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
          <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Page Header */}
        <div className="mb-12">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-3">
              {getStepInfo().title}
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              {getStepInfo().description}
            </p>
          </div>

          {/* Selected Template Preview - Enhanced */}
          {selectedTemplate && currentStep !== BatchStep.TEMPLATE_SELECTION && currentStep !== BatchStep.FILE_UPLOAD && (
            <div className="max-w-2xl mx-auto mb-8">
              <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-8">
                <div className="text-center">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Selected Template</h3>
                  <div className="w-full max-w-md mx-auto h-48 bg-gray-50 rounded-lg overflow-hidden shadow-sm border">
                    <img
                      src={selectedTemplate.preview}
                      alt={selectedTemplate.displayName}
                      className="w-full h-full object-contain"
                    />
                  </div>
                  <div className="mt-4">
                    <p className="text-lg font-medium text-gray-900">{selectedTemplate.displayName}</p>
                    <p className="text-sm text-gray-600 mt-1">{selectedTemplate.description}</p>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => setCurrentStep(BatchStep.TEMPLATE_SELECTION)}
                    className="mt-4 text-blue-600 border-blue-600 hover:bg-blue-50"
                    size="sm"
                  >
                    Change Template
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Step Indicator */}
          {currentStep !== BatchStep.LANDING && (
            <div className="flex items-center space-x-4">
              {[
                { step: BatchStep.TEMPLATE_SELECTION, label: 'Select Template' },
                { step: BatchStep.FILE_UPLOAD, label: 'Upload File' },
                { step: BatchStep.DATA_PREVIEW, label: 'Preview Data' },
                { step: BatchStep.GENERATION, label: 'Generate' },
                { step: BatchStep.COMPLETED, label: 'Complete' }
              ].map((item, index) => {
              const isActive = currentStep === item.step;
              const isCompleted = Object.values(BatchStep).indexOf(currentStep) > index;
              
              return (
                <div key={item.step} className="flex items-center">
                  <div className={`
                    w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                    ${isActive 
                      ? 'bg-blue-600 text-white' 
                      : isCompleted 
                        ? 'bg-green-600 text-white' 
                        : 'bg-gray-200 text-gray-600'
                    }
                  `}>
                    {index + 1}
                  </div>
                  <span className={`ml-2 text-sm ${isActive ? 'text-blue-600 font-medium' : 'text-gray-600'}`}>
                    {item.label}
                  </span>
                  {index < 4 && (
                    <div className={`w-8 h-0.5 mx-4 ${isCompleted ? 'bg-green-600' : 'bg-gray-200'}`} />
                  )}
                </div>
              );
            })}
            </div>
          )}
        </div>

        {/* Step Content */}
        <div className="space-y-8 max-w-4xl mx-auto">
          {currentStep === BatchStep.TEMPLATE_SELECTION && (
            <TemplateSelector
              selectedTemplate={selectedTemplate}
              onTemplateSelect={handleTemplateSelect}
              preSelectedCategory={getPreSelectedCategory()}
            />
          )}

          {currentStep === BatchStep.FILE_UPLOAD && (
            <div className="space-y-6">
              {/* Template Selection Summary - Enhanced Display */}
              {selectedTemplate && (
                <div className="bg-white rounded-xl border border-gray-200 shadow-lg p-8">
                  <div className="text-center mb-6">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">Selected Template</h3>
                    <p className="text-gray-600">Your chosen certificate template for bulk generation</p>
                  </div>

                  <div className="flex flex-col lg:flex-row items-center justify-center gap-8">
                    {/* Large Template Preview */}
                    <div className="flex-shrink-0">
                      <div className="w-80 h-60 bg-white rounded-lg border-2 border-gray-200 shadow-md overflow-hidden">
                        <img
                          src={selectedTemplate.preview}
                          alt={selectedTemplate.displayName}
                          className="w-full h-full object-contain"
                        />
                      </div>
                    </div>

                    {/* Template Info */}
                    <div className="text-center lg:text-left lg:flex-1 max-w-md">
                      <h4 className="text-xl font-semibold text-gray-900 mb-3">
                        {selectedTemplate.displayName}
                      </h4>
                      <p className="text-gray-600 mb-6 leading-relaxed">
                        {selectedTemplate.description}
                      </p>

                      <Button
                        variant="outline"
                        onClick={() => setCurrentStep(BatchStep.TEMPLATE_SELECTION)}
                        className="text-blue-600 border-blue-600 hover:bg-blue-50 px-6 py-3 text-lg font-medium"
                        size="lg"
                      >
                        <FileText className="w-5 h-5 mr-2" />
                        Change Template
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              <FileUploadZone
                onFileUploaded={handleFileUploaded}
                onError={handleFileUploadError}
              />
            </div>
          )}

          {currentStep === BatchStep.DATA_PREVIEW && uploadResult && (
            <DataPreview
              data={uploadResult.data || []}
              errors={uploadResult.errors}
              totalRows={uploadResult.totalRows || 0}
              validRows={uploadResult.validRows || 0}
              fileName={uploadedFileName}
              onConfirm={handleConfirmGeneration}
              onEdit={() => setCurrentStep(BatchStep.FILE_UPLOAD)}
              onChangeTemplate={() => setCurrentStep(BatchStep.TEMPLATE_SELECTION)}
              isLoading={isLoading}
            />
          )}

          {currentStep === BatchStep.GENERATION && taskId && (
            <ProgressMonitor
              taskId={taskId}
              onComplete={handleGenerationComplete}
              onError={handleGenerationError}
              onCancel={handleGenerationCancel}
            />
          )}

          {currentStep === BatchStep.COMPLETED && (
            <div className="text-center py-16">
              <div className="max-w-md mx-auto">
                <div className="mb-8">
                  <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                    <Download className="h-10 w-10 text-green-600" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-900 mb-3">
                    Generation Complete!
                  </h2>
                  <p className="text-lg text-gray-600">
                    All certificates have been successfully generated. You can download the ZIP file containing all your certificates.
                  </p>
                </div>

                <div className="space-y-4">
                  <Button
                    onClick={handleDownload}
                    className="bg-green-600 hover:bg-green-700 shadow-lg hover:shadow-xl transition-all duration-200"
                    size="lg"
                  >
                    <Download className="h-5 w-5 mr-2" />
                    Download Certificate Files
                  </Button>

                  <div>
                    <Button
                      variant="outline"
                      onClick={handleRestart}
                      className="border-2 hover:bg-gray-50"
                    >
                      Generate More Certificates
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
          </div>
        </div>
      )}
    </div>
  );
}
