'use client';

import React from 'react';
import { 
  FileText, 
  Upload, 
  Download, 
  CheckCircle, 
  Users, 
  Clock,
  Shield,
  Zap,
  ArrowRight,
  Star
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface BulkCertificateLandingProps {
  onGetStarted: () => void;
}

export default function BulkCertificateLanding({ onGetStarted }: BulkCertificateLandingProps) {
  const features = [
    {
      icon: <Upload className="h-8 w-8 text-blue-600" />,
      title: 'Easy File Upload',
      description: 'Simply upload your Excel or CSV file with recipient data and let our system handle the rest.',
      highlight: 'Supports .xlsx, .xls, .csv'
    },
    {
      icon: <Zap className="h-8 w-8 text-yellow-600" />,
      title: 'Lightning Fast',
      description: 'Generate hundreds of certificates in minutes with our optimized parallel processing engine.',
      highlight: 'Up to 1000 certificates'
    },
    {
      icon: <Shield className="h-8 w-8 text-green-600" />,
      title: 'Professional Quality',
      description: 'High-resolution PDF certificates with embedded fonts, perfect for printing or digital sharing.',
      highlight: 'Print-ready quality'
    },
    {
      icon: <Download className="h-8 w-8 text-purple-600" />,
      title: 'Bulk Download',
      description: 'All certificates are automatically packaged into a convenient ZIP file for easy download.',
      highlight: 'One-click download'
    }
  ];

  const steps = [
    {
      number: '01',
      title: 'Choose Template',
      description: 'Select from our professional certificate templates',
      icon: <FileText className="h-6 w-6" />
    },
    {
      number: '02',
      title: 'Upload Data',
      description: 'Upload your Excel or CSV file with recipient information',
      icon: <Upload className="h-6 w-6" />
    },
    {
      number: '03',
      title: 'Generate Bulk',
      description: 'Our system processes all certificates automatically',
      icon: <Users className="h-6 w-6" />
    },
    {
      number: '04',
      title: 'Download ZIP',
      description: 'Download all certificates in a single ZIP file',
      icon: <Download className="h-6 w-6" />
    }
  ];

  const benefits = [
    'Save hours of manual work',
    'Consistent professional formatting',
    'Error-free bulk processing',
    'Instant download ready',
    'Perfect for events & courses',
    'No software installation needed'
  ];

  return (
    <div className="space-y-16">
      {/* Breadcrumb Navigation */}
      <nav aria-label="Breadcrumb" className="text-sm">
        <ol className="flex items-center space-x-2 text-gray-500">
          <li>
            <a href="/" className="hover:text-blue-600 transition-colors">
              Home
            </a>
          </li>
          <li className="flex items-center">
            <span className="mx-2">/</span>
            <span className="text-gray-900 font-medium">Bulk Certificate Generator</span>
          </li>
        </ol>
      </nav>

      {/* Hero Section */}
      <section className="text-center space-y-8">
        <div className="space-y-4">
          <Badge variant="secondary" className="px-4 py-2 text-sm font-medium">
            <Star className="h-4 w-4 mr-2" />
            Most Popular Feature
          </Badge>
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 leading-tight">
            Bulk Certificate
            <span className="text-blue-600 block">Generator</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Generate hundreds of professional certificates in minutes. Perfect for schools,
            organizations, and events. Simply upload your data file and let our system
            create beautiful <a href="/certificate-templates" className="text-blue-600 hover:text-blue-700 underline">certificate templates</a> for all your recipients.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Button 
            size="lg" 
            onClick={onGetStarted}
            className="text-lg px-8 py-4 h-auto min-h-[56px] bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-200"
          >
            <Users className="w-5 h-5 mr-2" />
            Start Bulk Generation
            <ArrowRight className="w-5 h-5 ml-2" />
          </Button>
          <div className="text-sm text-gray-500">
            <Clock className="h-4 w-4 inline mr-1" />
            Generate 100+ certificates in under 5 minutes
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="space-y-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Why Choose Our Bulk Certificate Generator?
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Streamline your certificate creation process with powerful features designed for efficiency and quality.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <Card key={index} className="p-6 text-center hover:shadow-lg transition-shadow duration-200">
              <div className="flex justify-center mb-4">
                <div className="w-16 h-16 bg-gray-50 rounded-full flex items-center justify-center">
                  {feature.icon}
                </div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {feature.title}
              </h3>
              <p className="text-gray-600 text-sm mb-3">
                {feature.description}
              </p>
              <Badge variant="outline" className="text-xs">
                {feature.highlight}
              </Badge>
            </Card>
          ))}
        </div>
      </section>

      {/* How It Works */}
      <section className="space-y-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            How Bulk Certificate Generation Works
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Our streamlined 4-step process makes bulk certificate generation simple and efficient.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {steps.map((step, index) => (
            <div key={index} className="text-center space-y-4">
              <div className="relative">
                <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto text-xl font-bold">
                  {step.number}
                </div>
                {index < steps.length - 1 && (
                  <div className="hidden lg:block absolute top-8 left-full w-full h-0.5 bg-gray-200 -translate-y-0.5"></div>
                )}
              </div>
              <div className="space-y-2">
                <div className="flex justify-center text-blue-600">
                  {step.icon}
                </div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {step.title}
                </h3>
                <p className="text-gray-600 text-sm">
                  {step.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Benefits Section */}
      <section className="bg-blue-50 rounded-2xl p-8 md:p-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
          <div className="space-y-6">
            <h2 className="text-3xl font-bold text-gray-900">
              Perfect for Organizations & Events
            </h2>
            <p className="text-gray-600 text-lg">
              Whether you're running a training program, graduation ceremony, or corporate event, 
              our bulk certificate generator saves you time and ensures professional results every time.
            </p>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                  <span className="text-gray-700 text-sm">{benefit}</span>
                </div>
              ))}
            </div>
          </div>
          <div className="bg-white rounded-xl p-6 shadow-lg">
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-gray-900">File Format Requirements</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium text-gray-700">Column A</span>
                  <span className="text-gray-600">Recipient Name</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium text-gray-700">Column B</span>
                  <span className="text-gray-600">Date</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium text-gray-700">Column C</span>
                  <span className="text-gray-600">Signature</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium text-gray-700">Column D</span>
                  <span className="text-gray-600">Certificate Details</span>
                </div>
              </div>
              <p className="text-sm text-gray-500">
                Include a header row for best results. Maximum 1000 certificates per batch.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="space-y-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Frequently Asked Questions
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Everything you need to know about bulk certificate generation
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">How many certificates can I generate at once?</h3>
            <p className="text-gray-600">You can generate up to 1000 certificates in a single batch. For larger quantities, simply process multiple batches.</p>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">What file formats are supported?</h3>
            <p className="text-gray-600">We support Excel files (.xlsx, .xls) and CSV files. Maximum file size is 10MB per upload.</p>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">How long does bulk generation take?</h3>
            <p className="text-gray-600">Most batches complete within 2-5 minutes. Processing time depends on the number of certificates and template complexity.</p>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Can I customize the certificate templates?</h3>
            <p className="text-gray-600">Yes! Choose from our professional templates in Achievement, Completion, Participation, and Excellence categories.</p>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Is there a cost for bulk generation?</h3>
            <p className="text-gray-600">Our bulk certificate generator is completely free to use. Generate as many certificates as you need at no cost.</p>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">What data do I need to provide?</h3>
            <p className="text-gray-600">You need recipient names, dates, signatures, and certificate details. Our system guides you through the exact format required.</p>
          </div>
        </div>
      </section>

      {/* Features Overview Section */}
      <section className="py-16 bg-white rounded-2xl shadow-lg">
        <div className="max-w-4xl mx-auto px-8">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
            Why Choose Our Bulk Certificate Generator?
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                <Clock className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">Lightning Fast</h3>
              <p className="text-gray-600">Generate 100+ certificates in under 5 minutes. Our optimized system processes bulk requests efficiently.</p>
            </div>

            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <FileText className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">Professional Templates</h3>
              <p className="text-gray-600">Choose from dozens of professionally designed certificate templates for any occasion or achievement.</p>
            </div>

            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                <Upload className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">Easy File Upload</h3>
              <p className="text-gray-600">Simply upload your Excel or CSV file with recipient data. Supports .xlsx, .xls, and .csv formats.</p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
            How Bulk Certificate Generation Works
          </h2>

          <div className="space-y-8">
            <div className="flex items-start space-x-6">
              <div className="flex-shrink-0 w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-lg">
                1
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Prepare Your Data File</h3>
                <p className="text-gray-600">Create an Excel or CSV file with columns for recipient name, date, signature, and certificate details. Download our template to get started quickly.</p>
              </div>
            </div>

            <div className="flex items-start space-x-6">
              <div className="flex-shrink-0 w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-lg">
                2
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Select Certificate Template</h3>
                <p className="text-gray-600">Browse our collection of professional certificate templates. Choose from Achievement, Completion, Participation, and Excellence categories.</p>
              </div>
            </div>

            <div className="flex items-start space-x-6">
              <div className="flex-shrink-0 w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-lg">
                3
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Upload and Generate</h3>
                <p className="text-gray-600">Upload your data file, review the preview, and click generate. Our system will create all certificates and package them in a ZIP file for download.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl">
        <div className="max-w-4xl mx-auto px-8">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
            Perfect for Organizations & Events
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-6">
              <h3 className="text-xl font-semibold text-gray-900">Educational Institutions</h3>
              <ul className="space-y-3 text-gray-600">
                <li className="flex items-center">
                  <Star className="h-5 w-5 text-yellow-500 mr-3" />
                  Course completion certificates
                </li>
                <li className="flex items-center">
                  <Star className="h-5 w-5 text-yellow-500 mr-3" />
                  Academic achievement awards
                </li>
                <li className="flex items-center">
                  <Star className="h-5 w-5 text-yellow-500 mr-3" />
                  Training program certificates
                </li>
              </ul>
            </div>

            <div className="space-y-6">
              <h3 className="text-xl font-semibold text-gray-900">Corporate & Events</h3>
              <ul className="space-y-3 text-gray-600">
                <li className="flex items-center">
                  <Star className="h-5 w-5 text-yellow-500 mr-3" />
                  Employee recognition awards
                </li>
                <li className="flex items-center">
                  <Star className="h-5 w-5 text-yellow-500 mr-3" />
                  Conference participation certificates
                </li>
                <li className="flex items-center">
                  <Star className="h-5 w-5 text-yellow-500 mr-3" />
                  Workshop completion certificates
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="text-center space-y-6 py-16">
        <h2 className="text-3xl font-bold text-gray-900">
          Ready to Generate Bulk Certificates?
        </h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Join thousands of organizations who trust our bulk certificate generator
          for their important events and programs. Start creating professional certificates in minutes.
        </p>
        <Button
          size="lg"
          onClick={onGetStarted}
          className="text-lg px-8 py-4 h-auto min-h-[56px] bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-200"
        >
          <Users className="w-5 h-5 mr-2" />
          Start Bulk Generation Now
          <ArrowRight className="w-5 h-5 ml-2" />
        </Button>

        <div className="flex items-center justify-center space-x-8 text-sm text-gray-500 mt-8">
          <div className="flex items-center">
            <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
            100% Free to Use
          </div>
          <div className="flex items-center">
            <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
            No Registration Required
          </div>
          <div className="flex items-center">
            <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
            Instant Download
          </div>
        </div>
      </section>
    </div>
  );
}
