'use client';

import React, { useEffect, useState } from 'react';
import { 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  Download, 
  X,
  Loader2,
  FileText
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { BatchGenerationProgress, BatchTaskStatus } from '@/types/certificate';

interface ProgressMonitorProps {
  taskId: string;
  onComplete: (downloadUrl: string) => void;
  onError: (error: string) => void;
  onCancel: () => void;
}

export default function ProgressMonitor({
  taskId,
  onComplete,
  onError,
  onCancel
}: ProgressMonitorProps) {
  const [progress, setProgress] = useState<BatchGenerationProgress | null>(null);
  const [isPolling, setIsPolling] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 轮询进度
  useEffect(() => {
    if (!isPolling || !taskId) return;

    const pollProgress = async () => {
      try {
        const response = await fetch(`/api/batch/progress/${taskId}`);
        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || '获取进度失败');
        }

        setProgress(result.progress);

        // 检查是否完成
        if (result.progress.status === BatchTaskStatus.COMPLETED) {
          setIsPolling(false);
          onComplete(`/api/batch/download/${taskId}`);
        } else if (result.progress.status === BatchTaskStatus.FAILED) {
          setIsPolling(false);
          setError('生成失败');
          onError('批量生成失败');
        } else if (result.progress.status === BatchTaskStatus.CANCELLED) {
          setIsPolling(false);
          setError('任务已取消');
        }

      } catch (err) {
        console.error('Progress polling error:', err);
        setError(err instanceof Error ? err.message : '获取进度失败');
        setIsPolling(false);
        onError(err instanceof Error ? err.message : '获取进度失败');
      }
    };

    // 立即执行一次
    pollProgress();

    // 设置定时器
    const interval = setInterval(pollProgress, 2000); // 每2秒轮询一次

    return () => clearInterval(interval);
  }, [taskId, isPolling, onComplete, onError]);

  // 取消任务
  const handleCancel = async () => {
    try {
      const response = await fetch(`/api/batch/progress/${taskId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        setIsPolling(false);
        onCancel();
      } else {
        const result = await response.json();
        setError(result.error || '取消失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '取消失败');
    }
  };

  // 格式化时间
  const formatTime = (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds}秒`;
    } else if (seconds < 3600) {
      return `${Math.floor(seconds / 60)}分${seconds % 60}秒`;
    } else {
      return `${Math.floor(seconds / 3600)}小时${Math.floor((seconds % 3600) / 60)}分`;
    }
  };

  // 获取状态显示
  const getStatusDisplay = (status: BatchTaskStatus) => {
    switch (status) {
      case BatchTaskStatus.PENDING:
        return {
          icon: <Clock className="h-5 w-5 text-yellow-500" />,
          text: 'Pending',
          color: 'bg-yellow-100 text-yellow-800'
        };
      case BatchTaskStatus.PROCESSING:
        return {
          icon: <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />,
          text: 'Generating',
          color: 'bg-blue-100 text-blue-800'
        };
      case BatchTaskStatus.COMPLETED:
        return {
          icon: <CheckCircle className="h-5 w-5 text-green-500" />,
          text: 'Completed',
          color: 'bg-green-100 text-green-800'
        };
      case BatchTaskStatus.FAILED:
        return {
          icon: <AlertCircle className="h-5 w-5 text-red-500" />,
          text: 'Failed',
          color: 'bg-red-100 text-red-800'
        };
      case BatchTaskStatus.CANCELLED:
        return {
          icon: <X className="h-5 w-5 text-gray-500" />,
          text: 'Cancelled',
          color: 'bg-gray-100 text-gray-800'
        };
      default:
        return {
          icon: <Clock className="h-5 w-5 text-gray-500" />,
          text: 'Unknown',
          color: 'bg-gray-100 text-gray-800'
        };
    }
  };

  if (!progress) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center space-x-3">
          <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
          <p className="text-gray-600">Loading task status...</p>
        </div>
      </Card>
    );
  }

  const statusDisplay = getStatusDisplay(progress.status);

  return (
    <Card className="p-8 shadow-lg border-0 bg-white">
      <div className="space-y-8">
        {/* 任务状态头部 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <FileText className="h-6 w-6 text-blue-600" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Bulk Generation Progress</h3>
              <p className="text-sm text-gray-600">Task ID: {taskId}</p>
            </div>
          </div>
          
          <Badge className={statusDisplay.color}>
            <div className="flex items-center space-x-1">
              {statusDisplay.icon}
              <span>{statusDisplay.text}</span>
            </div>
          </Badge>
        </div>

        {/* 进度条 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">
              Progress: {progress.completedCount + progress.failedCount} / {progress.totalCount}
            </span>
            <span className="text-sm text-gray-500">
              {progress.progress}%
            </span>
          </div>
          
          <Progress value={progress.progress} className="h-3" />
          
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>Completed: {progress.completedCount}</span>
            {progress.failedCount > 0 && (
              <span className="text-red-600">Failed: {progress.failedCount}</span>
            )}
            {progress.estimatedTimeRemaining && (
              <span>Estimated remaining: {formatTime(progress.estimatedTimeRemaining)}</span>
            )}
          </div>
        </div>

        {/* 当前处理项目 */}
        {progress.currentItem && progress.status === BatchTaskStatus.PROCESSING && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <p className="text-sm text-blue-800">
              Currently processing: {progress.currentItem}
            </p>
          </div>
        )}

        {/* 错误信息 */}
        {progress.errors.length > 0 && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <h4 className="font-medium text-red-900">Processing Errors</h4>
            </div>
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {progress.errors.slice(0, 5).map((error, index) => (
                <p key={index} className="text-sm text-red-700">
                  Row {error.rowIndex}: {error.message}
                </p>
              ))}
              {progress.errors.length > 5 && (
                <p className="text-sm text-red-600 font-medium">
                  {progress.errors.length - 5} more errors...
                </p>
              )}
            </div>
          </div>
        )}

        {/* 系统错误 */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
          <div>
            {(progress.status === BatchTaskStatus.PENDING || progress.status === BatchTaskStatus.PROCESSING) && (
              <Button
                variant="outline"
                onClick={handleCancel}
                className="text-red-600 border-red-300 hover:bg-red-50"
              >
                <X className="h-4 w-4 mr-1" />
                Cancel Task
              </Button>
            )}
          </div>

          <div>
            {progress.status === BatchTaskStatus.COMPLETED && (
              <Button
                onClick={() => onComplete(`/api/batch/download/${taskId}`)}
                className="bg-green-600 hover:bg-green-700"
              >
                <Download className="h-4 w-4 mr-2" />
                Download Certificates
              </Button>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
}
