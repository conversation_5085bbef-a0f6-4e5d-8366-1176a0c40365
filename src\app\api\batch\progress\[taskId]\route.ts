import { NextRequest, NextResponse } from 'next/server';
import { BatchTaskManager } from '@/lib/batch-task-manager';

interface RouteParams {
  params: {
    taskId: string;
  };
}

/**
 * 获取批量生成任务进度
 * GET /api/batch/progress/[taskId]
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { taskId } = params;

    if (!taskId) {
      return NextResponse.json(
        { error: '任务ID不能为空' },
        { status: 400 }
      );
    }

    console.log(`📊 Getting progress for task: ${taskId}`);

    // 获取任务进度
    const progress = BatchTaskManager.getTaskProgress(taskId);

    if (!progress) {
      return NextResponse.json(
        { error: '任务不存在或已过期' },
        { status: 404 }
      );
    }

    // 返回进度信息
    return NextResponse.json({
      success: true,
      progress
    });

  } catch (error) {
    console.error(`❌ Get progress error for task ${params.taskId}:`, error);
    
    return NextResponse.json(
      { 
        error: '获取进度失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

/**
 * 取消批量生成任务
 * DELETE /api/batch/progress/[taskId]
 */
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { taskId } = params;

    if (!taskId) {
      return NextResponse.json(
        { error: '任务ID不能为空' },
        { status: 400 }
      );
    }

    console.log(`🛑 Cancelling task: ${taskId}`);

    // 取消任务
    const cancelled = BatchTaskManager.cancelTask(taskId);

    if (!cancelled) {
      return NextResponse.json(
        { error: '任务无法取消（可能已完成或不存在）' },
        { status: 400 }
      );
    }

    console.log(`✅ Task cancelled successfully: ${taskId}`);

    return NextResponse.json({
      success: true,
      message: '任务已取消'
    });

  } catch (error) {
    console.error(`❌ Cancel task error for ${params.taskId}:`, error);
    
    return NextResponse.json(
      { 
        error: '取消任务失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
