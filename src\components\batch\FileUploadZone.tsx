'use client';

import React, { useCallback, useState } from 'react';
import { Upload, AlertCircle, CheckCircle, Download, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { FileUploadResult } from '@/types/certificate';
import { SampleTemplateGenerator } from '@/lib/sample-template-generator';

interface FileUploadZoneProps {
  onFileUploaded: (result: FileUploadResult) => void;
  onError: (error: string) => void;
  disabled?: boolean;
  maxFileSize?: number;
  supportedFormats?: string[];
}

export default function FileUploadZone({
  onFileUploaded,
  onError,
  disabled = false,
  maxFileSize = 10 * 1024 * 1024, // 10MB
  supportedFormats = ['.xlsx', '.xls', '.csv']
}: FileUploadZoneProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);

  // Handle template download
  const handleDownloadTemplate = useCallback((format: 'excel' | 'csv') => {
    try {
      if (format === 'excel') {
        SampleTemplateGenerator.downloadExcelTemplate('certificate_template.xlsx');
      } else {
        SampleTemplateGenerator.downloadCSVTemplate('certificate_template.csv');
      }
    } catch (error) {
      onError('Failed to download template file');
    }
  }, [onError]);

  // 处理文件拖拽
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (disabled) return;

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [disabled]);

  // 处理文件选择
  const handleFileSelect = useCallback(async (file: File) => {
    if (!file) return;

    // 验证文件类型
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
    if (!supportedFormats.includes(fileExtension)) {
      onError(`不支持的文件格式。支持的格式：${supportedFormats.join(', ')}`);
      return;
    }

    // 验证文件大小
    if (file.size > maxFileSize) {
      onError(`文件大小不能超过 ${Math.round(maxFileSize / 1024 / 1024)}MB`);
      return;
    }

    setUploadedFile(file);
    setIsUploading(true);

    try {
      // 创建表单数据
      const formData = new FormData();
      formData.append('file', file);
      formData.append('hasHeader', 'true');
      formData.append('skipEmptyRows', 'true');

      // 上传文件
      const response = await fetch('/api/batch/upload', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || '文件上传失败');
      }

      console.log('✅ File uploaded successfully:', result);
      onFileUploaded(result);

    } catch (error) {
      console.error('❌ File upload error:', error);
      onError(error instanceof Error ? error.message : '文件上传失败');
      setUploadedFile(null);
    } finally {
      setIsUploading(false);
    }
  }, [supportedFormats, maxFileSize, onFileUploaded, onError]);

  // 处理文件输入变化
  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  // 清除文件
  const handleClearFile = useCallback(() => {
    setUploadedFile(null);
  }, []);

  return (
    <Card className="p-8 shadow-lg border-0 bg-white">
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Upload Certificate Data File
          </h3>
          <p className="text-sm text-gray-600">
            Supports Excel (.xlsx, .xls) and CSV file formats
          </p>
        </div>

        {!uploadedFile ? (
          <div
            className={`
              border-2 border-dashed rounded-xl p-8 md:p-12 text-center transition-all duration-200
              ${isDragOver
                ? 'border-blue-400 bg-blue-50'
                : 'border-gray-300 hover:border-gray-400'
              }
              ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
            `}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={() => !disabled && document.getElementById('file-input')?.click()}
          >
            <input
              id="file-input"
              type="file"
              accept={supportedFormats.join(',')}
              onChange={handleFileInputChange}
              className="hidden"
              disabled={disabled}
            />

            <div className="space-y-4">
              <div className="flex justify-center">
                <Upload className={`h-12 w-12 ${isDragOver ? 'text-blue-500' : 'text-gray-400'}`} />
              </div>

              <div>
                <p className="text-lg font-medium text-gray-900 mb-1">
                  {isDragOver ? 'Drop file to upload' : 'Drag file here or click to select'}
                </p>
                <p className="text-sm text-gray-500">
                  Maximum file size: {Math.round(maxFileSize / 1024 / 1024)}MB
                </p>
              </div>

              <Button
                variant="outline"
                disabled={disabled}
                className="mt-4"
              >
                Select File
              </Button>
            </div>
          </div>
        ) : (
          <div className="border rounded-lg p-4 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  {isUploading ? (
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                  ) : (
                    <CheckCircle className="h-6 w-6 text-green-500" />
                  )}
                </div>
                <div>
                  <p className="font-medium text-gray-900">{uploadedFile.name}</p>
                  <p className="text-sm text-gray-500">
                    {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              </div>

              {!isUploading && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearFile}
                  disabled={disabled}
                >
                  Select Different File
                </Button>
              )}
            </div>

            {isUploading && (
              <div className="mt-3">
                <p className="text-sm text-blue-600">Parsing file...</p>
              </div>
            )}
          </div>
        )}

        {/* Template Download Section */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
          <div className="flex items-start space-x-3">
            <Download className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <p className="font-medium text-green-900 mb-2">Need a template file?</p>
              <p className="text-sm text-green-800 mb-3">
                Download our sample template with example data to get started quickly.
              </p>
              <div className="flex flex-col sm:flex-row gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDownloadTemplate('excel')}
                  className="border-green-300 text-green-700 hover:bg-green-100"
                >
                  <FileText className="h-4 w-4 mr-1" />
                  Download Excel Template
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDownloadTemplate('csv')}
                  className="border-green-300 text-green-700 hover:bg-green-100"
                >
                  <FileText className="h-4 w-4 mr-1" />
                  Download CSV Template
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* File Format Requirements */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start space-x-2">
            <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="font-medium text-blue-900 mb-1">File Format Requirements:</p>
              <ul className="text-blue-800 space-y-1">
                <li>• Column A: Recipient Name</li>
                <li>• Column B: Date</li>
                <li>• Column C: Signature</li>
                <li>• Column D: Certificate Details</li>
                <li>• Header row recommended</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
