/**
 * 字体降级管理器
 * 提供优雅的字体降级和错误处理机制
 */

import { PDFDocument, PDFFont, StandardFonts } from 'pdf-lib';

export interface FallbackStrategy {
  family: string;
  weight: number;
  fallbacks: Array<{
    type: 'local' | 'google' | 'standard';
    family: string;
    weight?: number;
    standardFont?: StandardFonts;
  }>;
}

export interface FallbackResult {
  success: boolean;
  font?: PDFFont;
  usedFallback: boolean;
  fallbackLevel: number;
  strategy: string;
  error?: string;
}

/**
 * 字体降级管理器类
 */
export class FontFallbackManager {
  private pdfDoc: PDFDocument;
  private fallbackStrategies: Map<string, FallbackStrategy> = new Map();

  constructor(pdfDoc: PDFDocument) {
    this.pdfDoc = pdfDoc;
    this.initializeDefaultStrategies();
  }

  /**
   * 初始化默认降级策略
   */
  private initializeDefaultStrategies(): void {
    // Dancing Script 降级策略
    this.fallbackStrategies.set('Dancing Script', {
      family: 'Dancing Script',
      weight: 400,
      fallbacks: [
        { type: 'local', family: 'Dancing Script', weight: 400 },
        { type: 'google', family: 'Dancing Script', weight: 400 },
        { type: 'standard', family: 'Times Roman Italic', standardFont: StandardFonts.TimesRomanItalic },
        { type: 'standard', family: 'Times Roman', standardFont: StandardFonts.TimesRoman },
        { type: 'standard', family: 'Helvetica', standardFont: StandardFonts.Helvetica }
      ]
    });

    // Great Vibes 降级策略
    this.fallbackStrategies.set('Great Vibes', {
      family: 'Great Vibes',
      weight: 400,
      fallbacks: [
        { type: 'local', family: 'Great Vibes', weight: 400 },
        { type: 'google', family: 'Great Vibes', weight: 400 },
        { type: 'local', family: 'Dancing Script', weight: 400 },
        { type: 'standard', family: 'Times Roman Italic', standardFont: StandardFonts.TimesRomanItalic },
        { type: 'standard', family: 'Helvetica', standardFont: StandardFonts.Helvetica }
      ]
    });

    // Playfair Display 降级策略
    this.fallbackStrategies.set('Playfair Display', {
      family: 'Playfair Display',
      weight: 400,
      fallbacks: [
        { type: 'local', family: 'Playfair Display', weight: 400 },
        { type: 'google', family: 'Playfair Display', weight: 400 },
        { type: 'standard', family: 'Times Roman', standardFont: StandardFonts.TimesRoman },
        { type: 'standard', family: 'Helvetica', standardFont: StandardFonts.Helvetica }
      ]
    });

    // Inter 降级策略
    this.fallbackStrategies.set('Inter', {
      family: 'Inter',
      weight: 400,
      fallbacks: [
        { type: 'local', family: 'Inter', weight: 400 },
        { type: 'google', family: 'Inter', weight: 400 },
        { type: 'standard', family: 'Helvetica', standardFont: StandardFonts.Helvetica },
        { type: 'standard', family: 'Times Roman', standardFont: StandardFonts.TimesRoman }
      ]
    });
  }

  /**
   * 使用降级策略加载字体
   */
  async loadFontWithFallback(family: string, weight: number = 400): Promise<FallbackResult> {
    console.log(`🔄 Loading font with fallback: ${family} ${weight}`);

    const strategy = this.getFallbackStrategy(family, weight);
    
    for (let i = 0; i < strategy.fallbacks.length; i++) {
      const fallback = strategy.fallbacks[i];
      
      try {
        console.log(`🔧 Trying fallback ${i + 1}/${strategy.fallbacks.length}: ${fallback.type} - ${fallback.family}`);
        
        let font: PDFFont | null = null;

        switch (fallback.type) {
          case 'local':
            font = await this.tryLoadLocalFont(fallback.family, fallback.weight || weight);
            break;
          case 'google':
            font = await this.tryLoadGoogleFont(fallback.family, fallback.weight || weight);
            break;
          case 'standard':
            font = await this.tryLoadStandardFont(fallback.standardFont!);
            break;
        }

        if (font) {
          const isOriginalFont = i === 0;
          console.log(`✅ Font loaded successfully: ${fallback.family} (fallback level: ${i})`);
          
          return {
            success: true,
            font,
            usedFallback: !isOriginalFont,
            fallbackLevel: i,
            strategy: `${fallback.type}:${fallback.family}`,
          };
        }

      } catch (error) {
        console.warn(`⚠️ Fallback ${i + 1} failed:`, error);
        continue;
      }
    }

    // 所有降级都失败了
    console.error(`❌ All fallbacks failed for ${family} ${weight}`);
    
    return {
      success: false,
      usedFallback: true,
      fallbackLevel: -1,
      strategy: 'none',
      error: 'All fallback strategies failed'
    };
  }

  /**
   * 获取降级策略
   */
  private getFallbackStrategy(family: string, weight: number): FallbackStrategy {
    const strategy = this.fallbackStrategies.get(family);
    
    if (strategy) {
      // 调整权重相关的降级
      const adjustedStrategy = { ...strategy };
      adjustedStrategy.fallbacks = strategy.fallbacks.map(fallback => ({
        ...fallback,
        weight: fallback.weight || this.adjustWeightForFallback(weight)
      }));
      return adjustedStrategy;
    }

    // 默认降级策略
    return {
      family,
      weight,
      fallbacks: [
        { type: 'google', family, weight },
        { type: 'standard', family: 'Helvetica', standardFont: weight >= 600 ? StandardFonts.HelveticaBold : StandardFonts.Helvetica },
        { type: 'standard', family: 'Times Roman', standardFont: weight >= 600 ? StandardFonts.TimesRomanBold : StandardFonts.TimesRoman }
      ]
    };
  }

  /**
   * 调整权重以适应降级
   */
  private adjustWeightForFallback(weight: number): number {
    if (weight >= 700) return 700;
    if (weight >= 600) return 600;
    if (weight >= 500) return 500;
    return 400;
  }

  /**
   * 尝试加载本地字体
   */
  private async tryLoadLocalFont(family: string, weight: number): Promise<PDFFont | null> {
    const fontPaths: Record<string, Record<number, string>> = {
      'Dancing Script': {
        400: '/fonts/Dancing_Script/DancingScript-Regular.ttf',
        500: '/fonts/Dancing_Script/DancingScript-Medium.ttf',
        600: '/fonts/Dancing_Script/DancingScript-SemiBold.ttf',
        700: '/fonts/Dancing_Script/DancingScript-Bold.ttf'
      },
      'Open Sans': {
        400: '/fonts/open-sans/open-sans-400.ttf',
        700: '/fonts/open-sans/open-sans-700.ttf'
      },
      'Roboto': {
        400: '/fonts/roboto/roboto-400.ttf',
        700: '/fonts/roboto/roboto-700.ttf'
      }
    };

    const fontPath = fontPaths[family]?.[weight];
    if (!fontPath) {
      return null;
    }

    try {
      const response = await fetch(fontPath);
      if (!response.ok) {
        return null;
      }

      const fontBytes = await response.arrayBuffer();
      return await this.pdfDoc.embedFont(fontBytes, { subset: true });

    } catch (error) {
      console.warn(`Local font loading failed: ${family} ${weight}`, error);
      return null;
    }
  }

  /**
   * 尝试加载Google字体
   */
  private async tryLoadGoogleFont(family: string, weight: number): Promise<PDFFont | null> {
    try {
      // 获取CSS
      const cssUrl = `https://fonts.googleapis.com/css2?family=${encodeURIComponent(family)}:wght@${weight}&display=swap`;
      const cssResponse = await fetch(cssUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      if (!cssResponse.ok) {
        return null;
      }

      const cssText = await cssResponse.text();
      const urlMatch = cssText.match(/url\(([^)]+\.(?:woff2|woff|ttf))\)/);
      
      if (!urlMatch) {
        return null;
      }

      const fontUrl = urlMatch[1].replace(/['"]/g, '');
      
      // 下载字体
      const fontResponse = await fetch(fontUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      if (!fontResponse.ok) {
        return null;
      }

      const fontBytes = await fontResponse.arrayBuffer();
      return await this.pdfDoc.embedFont(fontBytes, { subset: true });

    } catch (error) {
      console.warn(`Google font loading failed: ${family} ${weight}`, error);
      return null;
    }
  }

  /**
   * 尝试加载标准字体
   */
  private async tryLoadStandardFont(standardFont: StandardFonts): Promise<PDFFont | null> {
    try {
      return await this.pdfDoc.embedFont(standardFont);
    } catch (error) {
      console.warn(`Standard font loading failed: ${standardFont}`, error);
      return null;
    }
  }

  /**
   * 添加自定义降级策略
   */
  addFallbackStrategy(strategy: FallbackStrategy): void {
    this.fallbackStrategies.set(strategy.family, strategy);
    console.log(`📝 Added custom fallback strategy for ${strategy.family}`);
  }

  /**
   * 获取所有降级策略
   */
  getAllStrategies(): Map<string, FallbackStrategy> {
    return new Map(this.fallbackStrategies);
  }

  /**
   * 测试降级策略
   */
  async testFallbackStrategy(family: string, weight: number = 400): Promise<{
    strategy: FallbackStrategy;
    results: Array<{
      fallback: any;
      success: boolean;
      error?: string;
    }>;
  }> {
    const strategy = this.getFallbackStrategy(family, weight);
    const results = [];

    for (const fallback of strategy.fallbacks) {
      try {
        let font: PDFFont | null = null;

        switch (fallback.type) {
          case 'local':
            font = await this.tryLoadLocalFont(fallback.family, fallback.weight || weight);
            break;
          case 'google':
            font = await this.tryLoadGoogleFont(fallback.family, fallback.weight || weight);
            break;
          case 'standard':
            font = await this.tryLoadStandardFont(fallback.standardFont!);
            break;
        }

        results.push({
          fallback,
          success: font !== null
        });

      } catch (error) {
        results.push({
          fallback,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return { strategy, results };
  }
}
