import { PDFDocument } from 'pdf-lib';
import fontkit from '@pdf-lib/fontkit';
import { 
  BatchCertificateData, 
  BatchGenerationError,
  CertificateTemplate,
  CertificateData
} from '@/types/certificate';
import { TemplateManager } from '@/lib/template-manager';
import { PDFTemplateGenerator } from '@/lib/pdf-template-generator';
import { TemplateFactory } from '@/lib/template-adapter';
import { BatchTaskManager } from '@/lib/batch-task-manager';
import { BatchPerformanceMonitor } from '@/lib/batch-performance-monitor';
import path from 'path';
import fs from 'fs/promises';

/**
 * 批量PDF生成器
 * 负责批量生成证书PDF文件
 */
export class BatchPDFGenerator {
  private taskId: string;
  private template: CertificateTemplate;
  private certificates: BatchCertificateData[];
  private outputDir: string;
  private maxConcurrency: number;

  constructor(
    taskId: string,
    template: CertificateTemplate,
    certificates: BatchCertificateData[],
    options: {
      outputDir?: string;
      maxConcurrency?: number;
    } = {}
  ) {
    this.taskId = taskId;
    this.template = template;
    this.certificates = certificates;
    this.outputDir = options.outputDir || this.getDefaultOutputDir();
    this.maxConcurrency = options.maxConcurrency || 3; // 默认并发数
  }

  /**
   * 开始批量生成
   */
  async generate(): Promise<{
    success: boolean;
    generatedFiles: string[];
    errors: BatchGenerationError[];
  }> {
    console.log(`🚀 Starting batch PDF generation for task: ${this.taskId}`);
    console.log(`📊 Total certificates: ${this.certificates.length}`);
    console.log(`🎯 Template: ${this.template.displayName}`);

    // 确保输出目录存在
    await this.ensureOutputDirectory();

    // 开始性能监控
    BatchPerformanceMonitor.startTask(this.taskId, this.certificates.length);

    // 更新任务状态为处理中
    BatchTaskManager.updateTaskStatus(this.taskId, 'processing' as any);

    const generatedFiles: string[] = [];
    const errors: BatchGenerationError[] = [];
    let completedCount = 0;

    try {
      // 分批处理证书
      const batches = this.createBatches(this.certificates, this.maxConcurrency);
      
      for (const batch of batches) {
        // 并行处理当前批次
        const batchPromises = batch.map(async (certificate, index) => {
          const itemStartTime = Date.now();

          try {
            const filename = await this.generateSinglePDF(certificate);
            generatedFiles.push(filename);
            completedCount++;

            // 记录性能指标
            const processingTime = Date.now() - itemStartTime;
            BatchPerformanceMonitor.recordItemCompleted(this.taskId, processingTime);

            // 更新进度
            BatchTaskManager.updateTaskProgress(this.taskId, completedCount, errors.length);

            console.log(`✅ Generated PDF ${completedCount}/${this.certificates.length}: ${filename} (${processingTime}ms)`);

          } catch (error) {
            const batchError: BatchGenerationError = {
              rowIndex: certificate.rowIndex || 0,
              message: error instanceof Error ? error.message : '生成PDF失败',
              certificateData: certificate
            };

            errors.push(batchError);
            BatchTaskManager.addTaskError(this.taskId, batchError);

            // 记录性能指标
            BatchPerformanceMonitor.recordItemFailed(this.taskId, batchError.message);

            console.error(`❌ Failed to generate PDF for certificate ${certificate.rowIndex}:`, error);
          }
        });

        // 等待当前批次完成
        await Promise.all(batchPromises);
      }

      // 结束性能监控
      BatchPerformanceMonitor.endTask(this.taskId);

      // 获取性能摘要和建议
      const performanceSummary = BatchPerformanceMonitor.generateSummary(this.taskId);
      const recommendations = BatchPerformanceMonitor.getPerformanceRecommendations(this.taskId);

      // 更新最终状态
      const success = errors.length === 0;
      BatchTaskManager.updateTaskStatus(
        this.taskId,
        success ? 'completed' as any : 'completed' as any
      );

      console.log(`🎉 Batch generation completed. Success: ${generatedFiles.length}, Errors: ${errors.length}`);
      console.log(`📊 Performance Summary:`, performanceSummary);

      if (recommendations.length > 0) {
        console.log(`💡 Performance Recommendations:`, recommendations);
      }

      return {
        success,
        generatedFiles,
        errors
      };

    } catch (error) {
      console.error('❌ Batch generation failed:', error);
      BatchTaskManager.updateTaskStatus(this.taskId, 'failed' as any);
      
      throw error;
    }
  }

  /**
   * 生成单个PDF
   */
  private async generateSinglePDF(certificate: BatchCertificateData): Promise<string> {
    try {
      // 转换为标准证书数据格式
      const certificateData: CertificateData = {
        templateId: this.template.id,
        recipientName: certificate.recipientName,
        date: certificate.date,
        signature: certificate.signature,
        details: certificate.details
      };

      // 获取PDF模板配置
      const pdfConfig = TemplateFactory.getPDFConfig(this.template);
      
      // 创建PDF生成器
      const generator = new PDFTemplateGenerator(pdfConfig, certificateData);
      
      // 生成PDF字节数组
      const pdfBytes = await generator.generate();
      
      // 生成文件名
      const filename = this.generateFilename(certificate);
      const filepath = path.join(this.outputDir, filename);
      
      // 保存文件
      await fs.writeFile(filepath, pdfBytes);
      
      return filename;
      
    } catch (error) {
      console.error(`Failed to generate PDF for certificate:`, error);
      throw new Error(`PDF生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 生成文件名
   */
  private generateFilename(certificate: BatchCertificateData): string {
    // 清理文件名中的特殊字符
    const safeName = certificate.recipientName
      .replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_')
      .substring(0, 50); // 限制长度
    
    const timestamp = Date.now();
    const rowSuffix = certificate.rowIndex ? `_row${certificate.rowIndex}` : '';
    
    return `certificate_${safeName}${rowSuffix}_${timestamp}.pdf`;
  }

  /**
   * 创建批次
   */
  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * 确保输出目录存在
   */
  private async ensureOutputDirectory(): Promise<void> {
    try {
      await fs.access(this.outputDir);
    } catch {
      await fs.mkdir(this.outputDir, { recursive: true });
      console.log(`📁 Created output directory: ${this.outputDir}`);
    }
  }

  /**
   * 获取默认输出目录
   */
  private getDefaultOutputDir(): string {
    return path.join(process.cwd(), 'temp', 'batch-pdfs', this.taskId);
  }

  /**
   * 清理临时文件
   */
  async cleanup(): Promise<void> {
    try {
      await fs.rm(this.outputDir, { recursive: true, force: true });
      console.log(`🧹 Cleaned up temporary files for task: ${this.taskId}`);
    } catch (error) {
      console.error(`Failed to cleanup files for task ${this.taskId}:`, error);
    }
  }

  /**
   * 获取生成的文件列表
   */
  async getGeneratedFiles(): Promise<string[]> {
    try {
      const files = await fs.readdir(this.outputDir);
      return files.filter(file => file.endsWith('.pdf'));
    } catch {
      return [];
    }
  }

  /**
   * 获取输出目录路径
   */
  getOutputDirectory(): string {
    return this.outputDir;
  }
}
